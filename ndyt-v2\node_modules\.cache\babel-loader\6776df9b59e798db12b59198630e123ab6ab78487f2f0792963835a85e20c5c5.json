{"ast": null, "code": "import \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nexport default {\n  name: 'LoginView',\n  data() {\n    return {\n      username: '',\n      password: '',\n      team_pin: '',\n      busy: false,\n      error: ''\n    };\n  },\n  mounted() {\n    // Aggressive cache busting for login component\n    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\n\n    // Add cache-busting meta tags specifically for login\n    const metaTags = [{\n      name: 'cache-control',\n      content: 'no-cache, no-store, must-revalidate, max-age=0'\n    }, {\n      name: 'pragma',\n      content: 'no-cache'\n    }, {\n      name: 'expires',\n      content: '0'\n    }, {\n      name: 'last-modified',\n      content: new Date().toUTCString()\n    }, {\n      name: 'etag',\n      content: cacheBuster\n    }, {\n      name: 'login-cache-buster',\n      content: cacheBuster\n    }];\n    metaTags.forEach(tag => {\n      // Remove existing meta tags with same name\n      const existing = document.querySelector(`meta[http-equiv=\"${tag.name}\"]`);\n      if (existing) existing.remove();\n\n      // Add new meta tag\n      const meta = document.createElement('meta');\n      meta.setAttribute('http-equiv', tag.name);\n      meta.setAttribute('content', tag.content);\n      document.head.appendChild(meta);\n    });\n\n    // Force component refresh to prevent caching issues\n    this.$forceUpdate();\n\n    // Clear any cached form data\n    this.username = '';\n    this.password = '';\n    this.team_pin = '';\n    this.error = '';\n\n    // Clear only non-authentication browser storage\n    if (typeof Storage !== 'undefined') {\n      // Only clear sessionStorage, preserve localStorage auth tokens\n      sessionStorage.clear();\n    }\n\n    // Force browser to not cache this page\n    if (window.history && window.history.replaceState) {\n      const url = new URL(window.location.href);\n      url.searchParams.set('_cb', cacheBuster);\n      url.searchParams.set('_nocache', '1');\n      window.history.replaceState(null, null, url.toString());\n    }\n\n    // Add cache-busting attribute to component element\n    if (this.$el && this.$el.setAttribute) {\n      this.$el.setAttribute('data-login-cache-bust', cacheBuster);\n      this.$el.setAttribute('data-no-cache', 'true');\n    }\n  },\n  beforeUnmount() {\n    // Clear form data when component is destroyed\n    this.username = '';\n    this.password = '';\n    this.team_pin = '';\n    this.error = '';\n  },\n  methods: {\n    async doLogin() {\n      this.error = '';\n      if (!this.username || !this.password || !this.team_pin) {\n        this.error = 'يرجى تعبئة جميع الحقول';\n        return;\n      }\n      this.busy = true;\n      try {\n        const res = await fetch('/api/v1/ndyt-activities/auth/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            username: this.username,\n            password: this.password,\n            team_pin: this.team_pin\n          })\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');\n        localStorage.setItem('ndyt_token', data.token);\n        // optional: store user info\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\n        // store team pin for later submissions\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\n        const redirect = this.$route.query.redirect || '/';\n        this.$router.replace(redirect);\n      } catch (e) {\n        this.error = e.message;\n      } finally {\n        this.busy = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "username", "password", "team_pin", "busy", "error", "mounted", "cacheBuster", "Date", "now", "Math", "random", "toString", "substr", "metaTags", "content", "toUTCString", "for<PERSON>ach", "tag", "existing", "document", "querySelector", "remove", "meta", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "$forceUpdate", "Storage", "sessionStorage", "clear", "window", "history", "replaceState", "url", "URL", "location", "href", "searchParams", "set", "$el", "beforeUnmount", "methods", "do<PERSON><PERSON><PERSON>", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "json", "ok", "Error", "localStorage", "setItem", "token", "user", "redirect", "$route", "query", "$router", "replace", "e", "message"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\LoginView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'LoginView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      team_pin: '',\r\n      busy: false,\r\n      error: ''\r\n    };\r\n  },\r\n  mounted() {\r\n    // Aggressive cache busting for login component\r\n    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\r\n    \r\n    // Add cache-busting meta tags specifically for login\r\n    const metaTags = [\r\n      { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },\r\n      { name: 'pragma', content: 'no-cache' },\r\n      { name: 'expires', content: '0' },\r\n      { name: 'last-modified', content: new Date().toUTCString() },\r\n      { name: 'etag', content: cacheBuster },\r\n      { name: 'login-cache-buster', content: cacheBuster }\r\n    ];\r\n    \r\n    metaTags.forEach(tag => {\r\n      // Remove existing meta tags with same name\r\n      const existing = document.querySelector(`meta[http-equiv=\"${tag.name}\"]`);\r\n      if (existing) existing.remove();\r\n      \r\n      // Add new meta tag\r\n      const meta = document.createElement('meta');\r\n      meta.setAttribute('http-equiv', tag.name);\r\n      meta.setAttribute('content', tag.content);\r\n      document.head.appendChild(meta);\r\n    });\r\n    \r\n    // Force component refresh to prevent caching issues\r\n    this.$forceUpdate();\r\n    \r\n    // Clear any cached form data\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n    \r\n    // Clear only non-authentication browser storage\r\n    if (typeof Storage !== 'undefined') {\r\n      // Only clear sessionStorage, preserve localStorage auth tokens\r\n      sessionStorage.clear();\r\n    }\r\n    \r\n    // Force browser to not cache this page\r\n    if (window.history && window.history.replaceState) {\r\n      const url = new URL(window.location.href);\r\n      url.searchParams.set('_cb', cacheBuster);\r\n      url.searchParams.set('_nocache', '1');\r\n      window.history.replaceState(null, null, url.toString());\r\n    }\r\n    \r\n    // Add cache-busting attribute to component element\r\n    if (this.$el && this.$el.setAttribute) {\r\n      this.$el.setAttribute('data-login-cache-bust', cacheBuster);\r\n      this.$el.setAttribute('data-no-cache', 'true');\r\n    }\r\n  },\r\n  beforeUnmount() {\r\n    // Clear form data when component is destroyed\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n  },\r\n  methods: {\r\n    async doLogin() {\r\n      this.error = '';\r\n      if (!this.username || !this.password || !this.team_pin) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/login', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, password: this.password, team_pin: this.team_pin })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        // optional: store user info\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        // store team pin for later submissions\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"login-container\">\r\n    <!-- Animated Background -->\r\n    <div class=\"animated-bg\">\r\n      <div class=\"floating-shapes\">\r\n        <div class=\"shape shape-1\"></div>\r\n        <div class=\"shape shape-2\"></div>\r\n        <div class=\"shape shape-3\"></div>\r\n        <div class=\"shape shape-4\"></div>\r\n        <div class=\"shape shape-5\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Header Section -->\r\n    <header class=\"header-section\">\r\n      <div class=\"header-content\">\r\n        <div class=\"logo-container\">\r\n          <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n          <div class=\"logo-glow\"></div>\r\n        </div>\r\n        <div class=\"header-text\">\r\n          <h2 class=\"org-name\">المجلس الأعلى للشباب</h2>\r\n          <h3 class=\"team-name\">الفريق الوطني للشباب الرقمي</h3>\r\n        </div>\r\n      </div>\r\n    </header>\r\n\r\n    <!-- Main Content -->\r\n    <main class=\"main-content\">\r\n      <div class=\"login-card\">\r\n        <div class=\"card-header\">\r\n          <h1 class=\"card-title\">تسجيل الدخول</h1>\r\n          <p class=\"card-subtitle\">مرحباً بك مرة أخرى</p>\r\n        </div>\r\n\r\n        <form class=\"login-form\" @submit.prevent=\"doLogin\">\r\n          <div class=\"input-group\">\r\n            <div class=\"input-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z\" fill=\"currentColor\"/>\r\n                </svg>\r\n              </div>\r\n              <input\r\n                v-model=\"username\"\r\n                type=\"text\"\r\n                dir=\"rtl\"\r\n                placeholder=\"\"\r\n                class=\"form-input\"\r\n                required\r\n              />\r\n              <label class=\"floating-label\">اسم المستخدم</label>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group\">\r\n            <div class=\"input-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z\" fill=\"currentColor\"/>\r\n                </svg>\r\n              </div>\r\n              <input\r\n                v-model=\"password\"\r\n                type=\"password\"\r\n                dir=\"rtl\"\r\n                placeholder=\"\"\r\n                class=\"form-input\"\r\n                required\r\n              />\r\n              <label class=\"floating-label\">كلمة المرور</label>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group\">\r\n            <div class=\"input-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </div>\r\n              <input\r\n                v-model=\"team_pin\"\r\n                type=\"password\"\r\n                dir=\"rtl\"\r\n                placeholder=\"\"\r\n                class=\"form-input\"\r\n                required\r\n              />\r\n              <label class=\"floating-label\">رمز الفريق الرقمي</label>\r\n            </div>\r\n          </div>\r\n\r\n          <button type=\"submit\" :disabled=\"busy\" class=\"login-btn\">\r\n            <span v-if=\"!busy\" class=\"btn-content\">\r\n              <span class=\"btn-text\">دخول</span>\r\n              <svg class=\"btn-icon\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M14 7L19 12M19 12L14 17M19 12H5\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n            </span>\r\n            <span v-else class=\"btn-loading\">\r\n              <div class=\"loading-spinner\"></div>\r\n              <span>جاري الدخول...</span>\r\n            </span>\r\n          </button>\r\n\r\n          <div v-if=\"error\" class=\"error-message\">\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z\" fill=\"currentColor\"/>\r\n            </svg>\r\n            <span>{{ error }}</span>\r\n          </div>\r\n        </form>\r\n\r\n        <div class=\"card-footer\">\r\n          <p class=\"register-link\">\r\n            ليس لديك حساب؟\r\n            <router-link to=\"/register\" class=\"link\">سجل الآن</router-link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n/* Global Styles */\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.login-container {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\r\n  direction: rtl;\r\n}\r\n\r\n/* Animated Background */\r\n.animated-bg {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.floating-shapes {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shape {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  background: linear-gradient(45deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));\r\n  animation: float 6s ease-in-out infinite;\r\n}\r\n\r\n.shape-1 {\r\n  width: 80px;\r\n  height: 80px;\r\n  top: 10%;\r\n  left: 10%;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.shape-2 {\r\n  width: 120px;\r\n  height: 120px;\r\n  top: 20%;\r\n  right: 15%;\r\n  animation-delay: 2s;\r\n}\r\n\r\n.shape-3 {\r\n  width: 60px;\r\n  height: 60px;\r\n  bottom: 30%;\r\n  left: 20%;\r\n  animation-delay: 4s;\r\n}\r\n\r\n.shape-4 {\r\n  width: 100px;\r\n  height: 100px;\r\n  bottom: 20%;\r\n  right: 10%;\r\n  animation-delay: 1s;\r\n}\r\n\r\n.shape-5 {\r\n  width: 140px;\r\n  height: 140px;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation-delay: 3s;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px) rotate(0deg);\r\n    opacity: 0.5;\r\n  }\r\n  50% {\r\n    transform: translateY(-20px) rotate(180deg);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n/* Header Section */\r\n.header-section {\r\n  position: relative;\r\n  z-index: 2;\r\n  padding: 1.5rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  backdrop-filter: blur(10px);\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 2rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.logo-container {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.logo {\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(79, 70, 229, 0.5);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.logo-glow {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 50%;\r\n  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    transform: translate(-50%, -50%) scale(1);\r\n    opacity: 0.5;\r\n  }\r\n  50% {\r\n    transform: translate(-50%, -50%) scale(1.1);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n.header-text {\r\n  text-align: center;\r\n}\r\n\r\n.org-name {\r\n  font-size: clamp(1.2rem, 4vw, 1.8rem);\r\n  font-weight: 800;\r\n  color: #f8fafc;\r\n  margin: 0 0 0.5rem 0;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.team-name {\r\n  font-size: clamp(1rem, 3vw, 1.4rem);\r\n  font-weight: 600;\r\n  color: #cbd5e1;\r\n  margin: 0;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n  position: relative;\r\n  z-index: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: calc(100vh - 140px);\r\n  padding: 2rem 1rem;\r\n}\r\n\r\n.login-card {\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.15);\r\n  border-radius: 24px;\r\n  padding: 2.5rem;\r\n  width: 100%;\r\n  max-width: 480px;\r\n  box-shadow:\r\n    0 20px 40px rgba(0, 0, 0, 0.4),\r\n    0 0 0 1px rgba(255, 255, 255, 0.05);\r\n  transition: all 0.4s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.login-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);\r\n}\r\n\r\n.login-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow:\r\n    0 32px 64px rgba(0, 0, 0, 0.5),\r\n    0 0 0 1px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Card Header */\r\n.card-header {\r\n  text-align: center;\r\n  margin-bottom: 2.5rem;\r\n}\r\n\r\n.login-icon {\r\n  width: 64px;\r\n  height: 64px;\r\n  margin: 0 auto 1.5rem;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);\r\n  animation: iconFloat 3s ease-in-out infinite;\r\n}\r\n\r\n.login-icon svg {\r\n  width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n@keyframes iconFloat {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n.card-title {\r\n  font-size: 2rem;\r\n  font-weight: 800;\r\n  margin: 0 0 0.5rem 0;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.card-subtitle {\r\n  color: #94a3b8;\r\n  font-size: 1rem;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Form Styles */\r\n.login-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.input-group {\r\n  position: relative;\r\n}\r\n\r\n.input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.input-icon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  color: #64748b;\r\n  z-index: 2;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  padding: 1rem 3rem 1rem 1rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 16px;\r\n  color: #f1f5f9;\r\n  font-size: 1rem;\r\n  transition: all 0.3s ease;\r\n  text-align: right;\r\n  direction: rtl;\r\n}\r\n\r\n.form-input:focus {\r\n  outline: none;\r\n  border-color: #4f46e5;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\n.form-input:focus + .floating-label,\r\n.form-input:not(:placeholder-shown) + .floating-label {\r\n  transform: translateY(-2.5rem) scale(0.85);\r\n  color: #ffffff;\r\n  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);\r\n}\r\n\r\n.form-input:focus ~ .input-icon {\r\n  color: #4f46e5;\r\n}\r\n\r\n.floating-label {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #64748b;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  pointer-events: none;\r\n  padding: 0 0.5rem;\r\n}\r\n\r\n/* Button Styles */\r\n.login-btn {\r\n  width: 100%;\r\n  padding: 1rem 2rem;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  border: none;\r\n  border-radius: 16px;\r\n  color: white;\r\n  font-size: 1.1rem;\r\n  font-weight: 700;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);\r\n  margin-top: 1rem;\r\n}\r\n\r\n.login-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.login-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.login-btn:hover {\r\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.login-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.login-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.btn-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.btn-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.login-btn:hover .btn-icon {\r\n  transform: translateX(-4px);\r\n}\r\n\r\n.btn-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-top: 2px solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Error Message */\r\n.error-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem;\r\n  background: rgba(239, 68, 68, 0.1);\r\n  border: 1px solid rgba(239, 68, 68, 0.3);\r\n  border-radius: 12px;\r\n  color: #fca5a5;\r\n  font-size: 0.9rem;\r\n  margin-top: 1rem;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n.error-message svg {\r\n  width: 20px;\r\n  height: 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Card Footer */\r\n.card-footer {\r\n  margin-top: 2rem;\r\n  text-align: center;\r\n}\r\n\r\n.register-link {\r\n  color: #94a3b8;\r\n  font-size: 0.95rem;\r\n  margin: 0;\r\n}\r\n\r\n.link {\r\n  color: #4f46e5;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.link::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: 0;\r\n  width: 0;\r\n  height: 2px;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.link:hover::after {\r\n  width: 100%;\r\n}\r\n\r\n.link:hover {\r\n  color: #6366f1;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .header-section {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1.5rem 1rem;\r\n    min-height: calc(100vh - 120px);\r\n  }\r\n\r\n  .login-card {\r\n    padding: 2rem;\r\n    border-radius: 20px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .login-icon {\r\n    width: 56px;\r\n    height: 56px;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .login-icon svg {\r\n    width: 28px;\r\n    height: 28px;\r\n  }\r\n\r\n  .form-input {\r\n    padding: 0.875rem 2.5rem 0.875rem 0.875rem;\r\n    font-size: 16px; /* Prevents zoom on iOS */\r\n  }\r\n\r\n  .login-btn {\r\n    padding: 0.875rem 1.5rem;\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .header-section {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 70px;\r\n    height: 70px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1rem 0.75rem;\r\n  }\r\n\r\n  .login-card {\r\n    padding: 1.5rem;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .card-subtitle {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .login-icon {\r\n    width: 48px;\r\n    height: 48px;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .login-icon svg {\r\n    width: 24px;\r\n    height: 24px;\r\n  }\r\n\r\n  .login-form {\r\n    gap: 1.25rem;\r\n  }\r\n\r\n  .form-input {\r\n    padding: 0.75rem 2.25rem 0.75rem 0.75rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .input-icon {\r\n    left: 0.75rem;\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .floating-label {\r\n    right: 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .login-btn {\r\n    padding: 0.75rem 1.25rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .btn-icon {\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .error-message {\r\n    padding: 0.75rem;\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .register-link {\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 360px) {\r\n  .login-card {\r\n    padding: 1.25rem;\r\n  }\r\n\r\n  .card-header {\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.375rem;\r\n  }\r\n\r\n  .form-input {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .floating-label {\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;AACA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,WAAU,GAAIC,IAAI,CAACC,GAAG,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;IAExE;IACA,MAAMC,QAAO,GAAI,CACf;MAAEf,IAAI,EAAE,eAAe;MAAEgB,OAAO,EAAE;IAAiD,CAAC,EACpF;MAAEhB,IAAI,EAAE,QAAQ;MAAEgB,OAAO,EAAE;IAAW,CAAC,EACvC;MAAEhB,IAAI,EAAE,SAAS;MAAEgB,OAAO,EAAE;IAAI,CAAC,EACjC;MAAEhB,IAAI,EAAE,eAAe;MAAEgB,OAAO,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC;IAAE,CAAC,EAC5D;MAAEjB,IAAI,EAAE,MAAM;MAAEgB,OAAO,EAAER;IAAY,CAAC,EACtC;MAAER,IAAI,EAAE,oBAAoB;MAAEgB,OAAO,EAAER;IAAY,EACpD;IAEDO,QAAQ,CAACG,OAAO,CAACC,GAAE,IAAK;MACtB;MACA,MAAMC,QAAO,GAAIC,QAAQ,CAACC,aAAa,CAAC,oBAAoBH,GAAG,CAACnB,IAAI,IAAI,CAAC;MACzE,IAAIoB,QAAQ,EAAEA,QAAQ,CAACG,MAAM,CAAC,CAAC;;MAE/B;MACA,MAAMC,IAAG,GAAIH,QAAQ,CAACI,aAAa,CAAC,MAAM,CAAC;MAC3CD,IAAI,CAACE,YAAY,CAAC,YAAY,EAAEP,GAAG,CAACnB,IAAI,CAAC;MACzCwB,IAAI,CAACE,YAAY,CAAC,SAAS,EAAEP,GAAG,CAACH,OAAO,CAAC;MACzCK,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;IACjC,CAAC,CAAC;;IAEF;IACA,IAAI,CAACK,YAAY,CAAC,CAAC;;IAEnB;IACA,IAAI,CAAC3B,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACE,KAAI,GAAI,EAAE;;IAEf;IACA,IAAI,OAAOwB,OAAM,KAAM,WAAW,EAAE;MAClC;MACAC,cAAc,CAACC,KAAK,CAAC,CAAC;IACxB;;IAEA;IACA,IAAIC,MAAM,CAACC,OAAM,IAAKD,MAAM,CAACC,OAAO,CAACC,YAAY,EAAE;MACjD,MAAMC,GAAE,GAAI,IAAIC,GAAG,CAACJ,MAAM,CAACK,QAAQ,CAACC,IAAI,CAAC;MACzCH,GAAG,CAACI,YAAY,CAACC,GAAG,CAAC,KAAK,EAAEjC,WAAW,CAAC;MACxC4B,GAAG,CAACI,YAAY,CAACC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;MACrCR,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAEC,GAAG,CAACvB,QAAQ,CAAC,CAAC,CAAC;IACzD;;IAEA;IACA,IAAI,IAAI,CAAC6B,GAAE,IAAK,IAAI,CAACA,GAAG,CAAChB,YAAY,EAAE;MACrC,IAAI,CAACgB,GAAG,CAAChB,YAAY,CAAC,uBAAuB,EAAElB,WAAW,CAAC;MAC3D,IAAI,CAACkC,GAAG,CAAChB,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;IAChD;EACF,CAAC;EACDiB,aAAaA,CAAA,EAAG;IACd;IACA,IAAI,CAACzC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACC,QAAO,GAAI,EAAE;IAClB,IAAI,CAACE,KAAI,GAAI,EAAE;EACjB,CAAC;EACDsC,OAAO,EAAE;IACP,MAAMC,OAAOA,CAAA,EAAG;MACd,IAAI,CAACvC,KAAI,GAAI,EAAE;MACf,IAAI,CAAC,IAAI,CAACJ,QAAO,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,QAAQ,EAAE;QACtD,IAAI,CAACE,KAAI,GAAI,wBAAwB;QACrC;MACF;MACA,IAAI,CAACD,IAAG,GAAI,IAAI;MAChB,IAAI;QACF,MAAMyC,GAAE,GAAI,MAAMC,KAAK,CAAC,oCAAoC,EAAE;UAC5DC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAElD,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,QAAQ,EAAE,IAAI,CAACA;UAAS,CAAC;QACpG,CAAC,CAAC;QACF,MAAMH,IAAG,GAAI,MAAM6C,GAAG,CAACO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACP,GAAG,CAACQ,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACtD,IAAI,CAACK,KAAI,IAAK,kBAAkB,CAAC;QAC9DkD,YAAY,CAACC,OAAO,CAAC,YAAY,EAAExD,IAAI,CAACyD,KAAK,CAAC;QAC9C;QACAF,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEN,IAAI,CAACC,SAAS,CAACnD,IAAI,CAAC0D,IAAG,IAAK,CAAC,CAAC,CAAC,CAAC;QAClE;QACAH,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,IAAI,CAACrD,QAAQ,CAAC;QACpD,MAAMwD,QAAO,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAO,IAAK,GAAG;QAClD,IAAI,CAACG,OAAO,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,EAAE,OAAOK,CAAC,EAAE;QACV,IAAI,CAAC3D,KAAI,GAAI2D,CAAC,CAACC,OAAO;MACxB,UAAU;QACR,IAAI,CAAC7D,IAAG,GAAI,KAAK;MACnB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}