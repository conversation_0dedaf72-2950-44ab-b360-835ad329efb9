{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"view\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_RouterView = _resolveComponent(\"RouterView\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [(_openBlock(), _createBlock(_component_RouterView, {\n    key: $options.routeKey\n  }))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createBlock", "_component_RouterView", "key", "$options", "routeKey"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\App.vue"], "sourcesContent": ["<script>\nimport { RouterView } from 'vue-router';\n\nexport default {\n  name: 'App',\n  components: {\n    RouterView\n  },\n  computed: {\n    routeKey() {\n      // Force component re-render on route change to prevent caching\n      return this.$route.fullPath + Date.now();\n    }\n  }\n};\n</script>\n<template>\n  <div class=\"view\">\n    <RouterView :key=\"routeKey\" />\n  </div>\n</template>\n<style>\n/* SweetAlert2 Custom Styles */\n.swal-popup {\n  background: linear-gradient(135deg, #1e1e2e, #2a2a3e) !important;\n  border: 2px solid rgba(255, 255, 255, 0.15) !important;\n  border-radius: 16px !important;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;\n}\n\n.swal2-title {\n  color: #f1f5f9 !important;\n  font-weight: 700 !important;\n  font-size: 20px !important;\n}\n\n.swal2-content {\n  color: #cbd5e1 !important;\n  font-size: 16px !important;\n}\n\n.swal-confirm-btn {\n  background: linear-gradient(135deg, #4f46e5, #7c3aed) !important;\n  border: none !important;\n  border-radius: 8px !important;\n  padding: 12px 24px !important;\n  font-weight: 600 !important;\n  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3) !important;\n  transition: all 0.3s ease !important;\n}\n\n.swal-confirm-btn:hover {\n  background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;\n  transform: translateY(-2px) !important;\n  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4) !important;\n}\n\n.swal-cancel-btn {\n  background: linear-gradient(135deg, #e53e3e, #c53030) !important;\n  border: none !important;\n  border-radius: 8px !important;\n  padding: 12px 24px !important;\n  font-weight: 600 !important;\n  box-shadow: 0 4px 16px rgba(229, 62, 62, 0.3) !important;\n  transition: all 0.3s ease !important;\n}\n\n.swal-cancel-btn:hover {\n  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;\n  transform: translateY(-2px) !important;\n  box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4) !important;\n}\n\n/* Toast Notifications Custom Styles */\n.Vue-Toastification__toast {\n  border-radius: 12px !important;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;\n  border: 2px solid rgba(255, 255, 255, 0.1) !important;\n  backdrop-filter: blur(10px) !important;\n}\n\n.toast-success {\n  background: linear-gradient(135deg, #059669, #047857) !important;\n  color: #ffffff !important;\n  border-color: rgba(5, 150, 105, 0.3) !important;\n}\n\n.toast-error {\n  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;\n  color: #ffffff !important;\n  border-color: rgba(220, 38, 38, 0.3) !important;\n}\n\n.toast-body {\n  font-weight: 600 !important;\n  font-size: 14px !important;\n  direction: rtl !important;\n  text-align: right !important;\n}\n\n.Vue-Toastification__progress-bar {\n  background: rgba(255, 255, 255, 0.3) !important;\n}\n\n.Vue-Toastification__close-button {\n  color: rgba(255, 255, 255, 0.8) !important;\n  font-size: 18px !important;\n  font-weight: bold !important;\n}\n\n.Vue-Toastification__close-button:hover {\n  color: #ffffff !important;\n}\n</style>"], "mappings": ";;EAiBOA,KAAK,EAAC;AAAM;;;uBAAjBC,mBAAA,CAEM,OAFNC,UAEM,I,cADJC,YAAA,CAA8BC,qBAAA;IAAjBC,GAAG,EAAEC,QAAA,CAAAC;EAAQ,I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}