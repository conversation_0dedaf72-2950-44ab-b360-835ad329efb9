{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, createTextVNode as _createTextVNode, withModifiers as _withModifiers, renderList as _renderList, Fragment as _Fragment, withKeys as _withKeys, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../assets/ndyt_logo.jpg';\nimport _imports_1 from '@/assets/scy_logo.jpg';\nimport _imports_2 from '@/assets/ndyt_logo.jpg';\nconst _hoisted_1 = {\n  class: \"navbar\"\n};\nconst _hoisted_2 = {\n  class: \"navbar-content\"\n};\nconst _hoisted_3 = {\n  class: \"nav-actions\"\n};\nconst _hoisted_4 = {\n  class: \"user-section\"\n};\nconst _hoisted_5 = {\n  class: \"user-info\"\n};\nconst _hoisted_6 = {\n  class: \"user-name\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"user-menu\"\n};\nconst _hoisted_8 = {\n  class: \"main-content\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"submit-view\"\n};\nconst _hoisted_10 = {\n  class: \"base-info-container\"\n};\nconst _hoisted_11 = {\n  class: \"activities-info-container\"\n};\nconst _hoisted_12 = {\n  key: 1,\n  class: \"view-activities\"\n};\nconst _hoisted_13 = {\n  class: \"view-header\"\n};\nconst _hoisted_14 = {\n  class: \"header-actions\"\n};\nconst _hoisted_15 = [\"disabled\"];\nconst _hoisted_16 = [\"disabled\"];\nconst _hoisted_17 = {\n  class: \"activities-container\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"loading-message\"\n};\nconst _hoisted_19 = {\n  key: 1,\n  class: \"no-activities-message\"\n};\nconst _hoisted_20 = {\n  class: \"edit-modal-header\"\n};\nconst _hoisted_21 = {\n  class: \"edit-form\"\n};\nconst _hoisted_22 = {\n  class: \"form-row\"\n};\nconst _hoisted_23 = {\n  class: \"form-group\"\n};\nconst _hoisted_24 = {\n  class: \"form-group\"\n};\nconst _hoisted_25 = {\n  class: \"form-row\"\n};\nconst _hoisted_26 = {\n  class: \"form-group\"\n};\nconst _hoisted_27 = {\n  class: \"form-group\"\n};\nconst _hoisted_28 = {\n  class: \"form-row\"\n};\nconst _hoisted_29 = {\n  class: \"form-group full-width\"\n};\nconst _hoisted_30 = {\n  class: \"form-actions\"\n};\nconst _hoisted_31 = {\n  class: \"activities-table-container\"\n};\nconst _hoisted_32 = {\n  class: \"table-header\"\n};\nconst _hoisted_33 = {\n  class: \"search-container\"\n};\nconst _hoisted_34 = {\n  class: \"search-box\"\n};\nconst _hoisted_35 = [\"title\", \"aria-label\"];\nconst _hoisted_36 = {\n  key: 0,\n  d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"\n};\nconst _hoisted_37 = {\n  key: 1,\n  d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n};\nconst _hoisted_38 = {\n  class: \"table-stats\"\n};\nconst _hoisted_39 = {\n  class: \"stat-number\"\n};\nconst _hoisted_40 = {\n  class: \"stat-number\"\n};\nconst _hoisted_41 = {\n  class: \"stat-number\"\n};\nconst _hoisted_42 = {\n  class: \"stat-number\"\n};\nconst _hoisted_43 = {\n  class: \"stat-number\"\n};\nconst _hoisted_44 = {\n  class: \"stat-number\"\n};\nconst _hoisted_45 = {\n  class: \"stat-number\"\n};\nconst _hoisted_46 = {\n  class: \"stat-number\"\n};\nconst _hoisted_47 = {\n  class: \"stat-number\"\n};\nconst _hoisted_48 = {\n  class: \"table-wrapper\"\n};\nconst _hoisted_49 = {\n  class: \"activities-table\"\n};\nconst _hoisted_50 = [\"onClick\"];\nconst _hoisted_51 = {\n  class: \"col-title\"\n};\nconst _hoisted_52 = {\n  class: \"activity-title\"\n};\nconst _hoisted_53 = {\n  class: \"activity-description\"\n};\nconst _hoisted_54 = {\n  class: \"col-owner\"\n};\nconst _hoisted_55 = {\n  class: \"owner-info\"\n};\nconst _hoisted_56 = {\n  class: \"col-date\"\n};\nconst _hoisted_57 = {\n  class: \"date-info\"\n};\nconst _hoisted_58 = {\n  class: \"date-main\"\n};\nconst _hoisted_59 = {\n  class: \"date-year\"\n};\nconst _hoisted_60 = {\n  class: \"col-status\"\n};\nconst _hoisted_61 = {\n  class: \"col-governorate\"\n};\nconst _hoisted_62 = {\n  class: \"governorate-name\"\n};\nconst _hoisted_63 = {\n  class: \"col-coordinator\"\n};\nconst _hoisted_64 = {\n  class: \"coordinator-name\"\n};\nconst _hoisted_65 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_66 = [\"onClick\"];\nconst _hoisted_67 = [\"onClick\"];\nconst _hoisted_68 = {\n  class: \"modal-header\"\n};\nconst _hoisted_69 = {\n  class: \"form-group\"\n};\nconst _hoisted_70 = {\n  class: \"form-group\"\n};\nconst _hoisted_71 = {\n  class: \"password-section\"\n};\nconst _hoisted_72 = {\n  class: \"form-group\"\n};\nconst _hoisted_73 = {\n  class: \"form-group\"\n};\nconst _hoisted_74 = {\n  class: \"form-group\"\n};\nconst _hoisted_75 = {\n  class: \"form-actions\"\n};\nconst _hoisted_76 = [\"disabled\"];\nconst _hoisted_77 = {\n  key: 0\n};\nconst _hoisted_78 = {\n  key: 1\n};\nconst _hoisted_79 = {\n  class: \"modal-header\"\n};\nconst _hoisted_80 = {\n  class: \"modal-body\"\n};\nconst _hoisted_81 = {\n  class: \"pin-message\"\n};\nconst _hoisted_82 = {\n  class: \"form-group\"\n};\nconst _hoisted_83 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_84 = [\"disabled\"];\nconst _hoisted_85 = {\n  class: \"modal-header\"\n};\nconst _hoisted_86 = {\n  class: \"modal-body activity-document\"\n};\nconst _hoisted_87 = {\n  class: \"document-header\"\n};\nconst _hoisted_88 = {\n  class: \"document-title\"\n};\nconst _hoisted_89 = {\n  class: \"details-table\"\n};\nconst _hoisted_90 = {\n  class: \"detail-row\"\n};\nconst _hoisted_91 = {\n  class: \"detail-value\"\n};\nconst _hoisted_92 = {\n  class: \"detail-row\"\n};\nconst _hoisted_93 = {\n  class: \"detail-value\"\n};\nconst _hoisted_94 = {\n  key: 0,\n  class: \"detail-row\"\n};\nconst _hoisted_95 = {\n  class: \"detail-value\"\n};\nconst _hoisted_96 = {\n  key: 1,\n  class: \"detail-row\"\n};\nconst _hoisted_97 = {\n  class: \"detail-value\"\n};\nconst _hoisted_98 = {\n  class: \"goals-list\"\n};\nconst _hoisted_99 = {\n  key: 2,\n  class: \"detail-row\"\n};\nconst _hoisted_100 = {\n  class: \"detail-value\"\n};\nconst _hoisted_101 = {\n  class: \"target-groups-list\"\n};\nconst _hoisted_102 = {\n  key: 3,\n  class: \"detail-row\"\n};\nconst _hoisted_103 = {\n  class: \"detail-value\"\n};\nconst _hoisted_104 = {\n  class: \"detail-row\"\n};\nconst _hoisted_105 = {\n  class: \"detail-value\"\n};\nconst _hoisted_106 = {\n  key: 4,\n  class: \"detail-row\"\n};\nconst _hoisted_107 = {\n  class: \"detail-value\"\n};\nconst _hoisted_108 = {\n  key: 5,\n  class: \"detail-row\"\n};\nconst _hoisted_109 = {\n  class: \"detail-value\"\n};\nconst _hoisted_110 = {\n  key: 6,\n  class: \"detail-row\"\n};\nconst _hoisted_111 = {\n  class: \"detail-value\"\n};\nconst _hoisted_112 = {\n  key: 7,\n  class: \"detail-row\"\n};\nconst _hoisted_113 = {\n  class: \"detail-value\"\n};\nconst _hoisted_114 = {\n  key: 8,\n  class: \"detail-row\"\n};\nconst _hoisted_115 = {\n  class: \"detail-value\"\n};\nconst _hoisted_116 = {\n  class: \"budget-table\"\n};\nconst _hoisted_117 = {\n  key: 0\n};\nconst _hoisted_118 = {\n  class: \"total-row\"\n};\nconst _hoisted_119 = {\n  key: 9,\n  class: \"detail-row\"\n};\nconst _hoisted_120 = {\n  class: \"detail-value\"\n};\nconst _hoisted_121 = {\n  class: \"levels-list\"\n};\nconst _hoisted_122 = {\n  class: \"detail-row\"\n};\nconst _hoisted_123 = {\n  class: \"detail-value\"\n};\nconst _hoisted_124 = {\n  class: \"detail-row\"\n};\nconst _hoisted_125 = {\n  class: \"detail-value\"\n};\nconst _hoisted_126 = {\n  class: \"detail-row\"\n};\nconst _hoisted_127 = {\n  class: \"detail-value\"\n};\nconst _hoisted_128 = {\n  class: \"detail-row\"\n};\nconst _hoisted_129 = {\n  class: \"detail-value\"\n};\nconst _hoisted_130 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_131 = {\n  width: \"16\",\n  height: \"16\",\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  style: {\n    \"margin-left\": \"8px\"\n  }\n};\nconst _hoisted_132 = {\n  width: \"16\",\n  height: \"16\",\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\",\n  style: {\n    \"margin-left\": \"8px\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[63] || (_cache[63] = _createElementVNode(\"div\", {\n    class: \"navbar-text\"\n  }, [_createElementVNode(\"span\", {\n    class: \"org-name\"\n  }, \"المجلس الأعلى للشباب\"), _createElementVNode(\"img\", {\n    class: \"logo\",\n    src: _imports_0,\n    alt: \"شعار الفريق\"\n  }), _createElementVNode(\"span\", {\n    class: \"team-name\"\n  }, \"الفريق الوطني للشباب الرقمي\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-btn\", {\n      active: $data.currentView === 'submit'\n    }]),\n    onClick: _cache[0] || (_cache[0] = $event => $options.setCurrentView('submit'))\n  }, [...(_cache[56] || (_cache[56] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"إرسال النشاطات\", -1 /* CACHED */)]))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-btn\", {\n      active: $data.currentView === 'view'\n    }]),\n    onClick: _cache[1] || (_cache[1] = $event => $options.setCurrentView('view'))\n  }, [...(_cache[57] || (_cache[57] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"النشاطات المرسلة\", -1 /* CACHED */)]))], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", {\n    class: \"user-button\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.toggleUserMenu && $options.toggleUserMenu(...args))\n  }, [_cache[59] || (_cache[59] = _createElementVNode(\"div\", {\n    class: \"user-avatar\"\n  }, [_createElementVNode(\"svg\", {\n    width: \"24\",\n    height: \"24\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n  })])], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, _toDisplayString($data.user?.full_name || $data.user?.username || 'المستخدم'), 1 /* TEXT */), _cache[58] || (_cache[58] = _createElementVNode(\"svg\", {\n    class: \"dropdown-arrow\",\n    width: \"16\",\n    height: \"16\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M7 10l5 5 5-5z\"\n  })], -1 /* CACHED */))])]), $data.showUserMenu ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", {\n    class: \"user-menu-item\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.openAccountSettings && $options.openAccountSettings(...args))\n  }, [...(_cache[60] || (_cache[60] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"إعدادات الحساب\", -1 /* CACHED */)]))]), $data.user && $data.user.rank === 'admin' ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"user-menu-item\",\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.goToAdmin && $options.goToAdmin(...args))\n  }, [...(_cache[61] || (_cache[61] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17.5L18 14.5V9H6V14.5L12 17.5Z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"لوحة الإدارة\", -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: \"user-menu-item\",\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.logout && $options.logout(...args))\n  }, [...(_cache[62] || (_cache[62] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"تسجيل الخروج\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true)])])]), _createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" Submit Activities View \"), $data.currentView === 'submit' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_cache[70] || (_cache[70] = _createElementVNode(\"div\", {\n    class: \"view-header\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"view-title\"\n  }, \"إرسال النشاطات الجديدة\"), _createElementVNode(\"p\", {\n    class: \"view-description\"\n  }, \"قم بإدخال المعلومات الأساسية ونشاطاتك لإرسالها للمراجعة\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, [_cache[64] || (_cache[64] = _createElementVNode(\"span\", {\n    class: \"base-info-label\"\n  }, \"المعلومات الأساسية\", -1 /* CACHED */)), _cache[65] || (_cache[65] = _createElementVNode(\"label\", {\n    for: \"coordinator-name\",\n    class: \"field-label\"\n  }, \"منسق المحافظة\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"coordinator-name\",\n    placeholder: \"اسم منسق المحافظة\",\n    class: \"base-info-input\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.coordinatorName = $event),\n    disabled: \"\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.coordinatorName]])]), _cache[71] || (_cache[71] = _createElementVNode(\"div\", {\n    class: \"splitter\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_11, [_cache[67] || (_cache[67] = _createElementVNode(\"span\", {\n    class: \"base-info-label\"\n  }, \"إضافة نشاطات جديدة\", -1 /* CACHED */)), _cache[68] || (_cache[68] = _createElementVNode(\"div\", {\n    class: \"activities-list\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"button\", {\n    style: {\n      \"margin\": \"10px\",\n      \"max-width\": \"250px\"\n    },\n    onClick: _cache[7] || (_cache[7] = (...args) => $options.AddActivityItem && $options.AddActivityItem(...args))\n  }, [...(_cache[66] || (_cache[66] = [_createElementVNode(\"span\", null, \"إضافة نشاط جديد\", -1 /* CACHED */)]))])]), _cache[72] || (_cache[72] = _createElementVNode(\"div\", {\n    class: \"splitter\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"button\", {\n    style: {\n      \"margin\": \"0 50px\",\n      \"background-color\": \"orange\",\n      \"max-width\": \"180px\"\n    },\n    onClick: _cache[8] || (_cache[8] = (...args) => $options.submitCV && $options.submitCV(...args))\n  }, [...(_cache[69] || (_cache[69] = [_createElementVNode(\"span\", null, \"إرسال النشاطات\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" View Activities Section \"), $data.currentView === 'view' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[76] || (_cache[76] = _createElementVNode(\"div\", {\n    class: \"view-header-content\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"view-title\"\n  }, \"النشاطات\"), _createElementVNode(\"p\", {\n    class: \"view-description\"\n  }, \"عرض وإدارة النشاطات حسب صلاحياتك (المدير: جميع النشاطات، المنسق: نشاطات المحافظة، العضو: نشاطاتك الشخصية)\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"button\", {\n    onClick: _cache[9] || (_cache[9] = (...args) => $options.exportToCSV && $options.exportToCSV(...args)),\n    class: \"export-btn\",\n    disabled: $data.loadingActivities || $options.filteredActivities.length === 0,\n    title: \"تصدير إلى Excel\"\n  }, [...(_cache[73] || (_cache[73] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n  })], -1 /* CACHED */), _createElementVNode(\"span\", null, \"تصدير CSV\", -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_15), _createElementVNode(\"button\", {\n    onClick: _cache[10] || (_cache[10] = (...args) => $options.refreshActivities && $options.refreshActivities(...args)),\n    class: \"refresh-btn\",\n    disabled: $data.loadingActivities\n  }, [(_openBlock(), _createElementBlock(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    class: _normalizeClass({\n      'spinning': $data.loadingActivities\n    })\n  }, [...(_cache[74] || (_cache[74] = [_createElementVNode(\"path\", {\n    d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"\n  }, null, -1 /* CACHED */)]))], 2 /* CLASS */)), _cache[75] || (_cache[75] = _createElementVNode(\"span\", null, \"تحديث\", -1 /* CACHED */))], 8 /* PROPS */, _hoisted_16)])]), _createElementVNode(\"div\", _hoisted_17, [$data.loadingActivities ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [...(_cache[77] || (_cache[77] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"جاري تحميل النشاطات...\", -1 /* CACHED */)]))])) : $data.myActivities.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [...(_cache[78] || (_cache[78] = [_createElementVNode(\"svg\", {\n    width: \"64\",\n    height: \"64\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    class: \"empty-icon\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n  })], -1 /* CACHED */), _createElementVNode(\"h3\", null, \"لا توجد نشاطات\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"لا توجد نشاطات متاحة حسب صلاحياتك حالياً\", -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Edit Form Modal \"), $data.editingActivity ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 2,\n    class: \"edit-modal-overlay\",\n    onClick: _cache[20] || (_cache[20] = (...args) => $options.cancelEdit && $options.cancelEdit(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"edit-modal\",\n    onClick: _cache[19] || (_cache[19] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_20, [_cache[80] || (_cache[80] = _createElementVNode(\"h3\", null, \"تعديل النشاط\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.cancelEdit && $options.cancelEdit(...args)),\n    class: \"close-btn\"\n  }, [...(_cache[79] || (_cache[79] = [_createElementVNode(\"svg\", {\n    width: \"24\",\n    height: \"24\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n  })], -1 /* CACHED */)]))])]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[81] || (_cache[81] = _createElementVNode(\"label\", null, \"اسم صاحب النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.editingActivity.owner_name = $event),\n    type: \"text\",\n    class: \"form-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.owner_name]])]), _createElementVNode(\"div\", _hoisted_24, [_cache[82] || (_cache[82] = _createElementVNode(\"label\", null, \"عنوان النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.editingActivity.title = $event),\n    type: \"text\",\n    class: \"form-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.title]])])]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[83] || (_cache[83] = _createElementVNode(\"label\", null, \"تاريخ النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.editingActivity.activity_date = $event),\n    type: \"date\",\n    class: \"form-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.activity_date]])]), _createElementVNode(\"div\", _hoisted_27, [_cache[85] || (_cache[85] = _createElementVNode(\"label\", null, \"حالة النشاط:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.editingActivity.state = $event),\n    class: \"form-select\"\n  }, [...(_cache[84] || (_cache[84] = [_createStaticVNode(\"<option value=\\\"مرسل\\\">مرسل</option><option value=\\\"منفذ بصرف\\\">منفذ بصرف</option><option value=\\\"منفذ بدون صرف\\\">منفذ بدون صرف</option><option value=\\\"مقبول\\\">مقبول</option><option value=\\\"مرفوض\\\">مرفوض</option><option value=\\\"يحتاج تعديل\\\">يحتاج تعديل</option><option value=\\\"صرف و لم ينفذ\\\">صرف و لم ينفذ</option><option value=\\\"مقبول دون صرف\\\">مقبول دون صرف</option>\", 8)]))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.editingActivity.state]])])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_cache[86] || (_cache[86] = _createElementVNode(\"label\", null, \"وصف مختصر:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.editingActivity.short_description = $event),\n    class: \"form-textarea\",\n    rows: \"3\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editingActivity.short_description]])])]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"button\", {\n    onClick: _cache[17] || (_cache[17] = (...args) => $options.saveActivity && $options.saveActivity(...args)),\n    class: \"save-btn\"\n  }, [...(_cache[87] || (_cache[87] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"\n  })], -1 /* CACHED */), _createTextVNode(\" حفظ التغييرات \", -1 /* CACHED */)]))]), _createElementVNode(\"button\", {\n    onClick: _cache[18] || (_cache[18] = (...args) => $options.cancelEdit && $options.cancelEdit(...args)),\n    class: \"cancel-btn\"\n  }, [...(_cache[88] || (_cache[88] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n  })], -1 /* CACHED */), _createTextVNode(\" إلغاء \", -1 /* CACHED */)]))])])])])])) : $data.myActivities.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" Activities Table \"), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createCommentVNode(\" Search Box \"), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.searchQuery = $event),\n    placeholder: \"البحث بعنوان النشاط أو اسم صاحب النشاط...\",\n    class: \"search-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.searchQuery]]), (_openBlock(), _createElementBlock(\"svg\", {\n    class: \"search-icon\",\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    onClick: _cache[22] || (_cache[22] = (...args) => $options.handleSearchIconClick && $options.handleSearchIconClick(...args)),\n    title: $data.searchQuery ? 'مسح البحث' : 'بحث',\n    \"aria-label\": $data.searchQuery ? 'مسح البحث' : 'بحث'\n  }, [!$data.searchQuery ? (_openBlock(), _createElementBlock(\"path\", _hoisted_36)) : (_openBlock(), _createElementBlock(\"path\", _hoisted_37))], 8 /* PROPS */, _hoisted_35))])]), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === null\n    }]),\n    onClick: _cache[23] || (_cache[23] = $event => $options.selectFilter(null))\n  }, [_createElementVNode(\"span\", _hoisted_39, _toDisplayString($data.myActivities.length), 1 /* TEXT */), _cache[89] || (_cache[89] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"إجمالي النشاطات\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'منفذ بصرف'\n    }]),\n    onClick: _cache[24] || (_cache[24] = $event => $options.selectFilter('منفذ بصرف'))\n  }, [_createElementVNode(\"span\", _hoisted_40, _toDisplayString($data.myActivities.filter(a => a.state === 'منفذ بصرف').length), 1 /* TEXT */), _cache[90] || (_cache[90] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"منفذ بصرف\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'منفذ بدون صرف'\n    }]),\n    onClick: _cache[25] || (_cache[25] = $event => $options.selectFilter('منفذ بدون صرف'))\n  }, [_createElementVNode(\"span\", _hoisted_41, _toDisplayString($data.myActivities.filter(a => a.state === 'منفذ بدون صرف').length), 1 /* TEXT */), _cache[91] || (_cache[91] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"منفذ بدون صرف\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مقبول'\n    }]),\n    onClick: _cache[26] || (_cache[26] = $event => $options.selectFilter('مقبول'))\n  }, [_createElementVNode(\"span\", _hoisted_42, _toDisplayString($data.myActivities.filter(a => a.state === 'مقبول').length), 1 /* TEXT */), _cache[92] || (_cache[92] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مقبول\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مرفوض'\n    }]),\n    onClick: _cache[27] || (_cache[27] = $event => $options.selectFilter('مرفوض'))\n  }, [_createElementVNode(\"span\", _hoisted_43, _toDisplayString($data.myActivities.filter(a => a.state === 'مرفوض').length), 1 /* TEXT */), _cache[93] || (_cache[93] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مرفوض\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'يحتاج تعديل'\n    }]),\n    onClick: _cache[28] || (_cache[28] = $event => $options.selectFilter('يحتاج تعديل'))\n  }, [_createElementVNode(\"span\", _hoisted_44, _toDisplayString($data.myActivities.filter(a => a.state === 'يحتاج تعديل').length), 1 /* TEXT */), _cache[94] || (_cache[94] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"يحتاج تعديل\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'صرف و لم ينفذ'\n    }]),\n    onClick: _cache[29] || (_cache[29] = $event => $options.selectFilter('صرف و لم ينفذ'))\n  }, [_createElementVNode(\"span\", _hoisted_45, _toDisplayString($data.myActivities.filter(a => a.state === 'صرف و لم ينفذ').length), 1 /* TEXT */), _cache[95] || (_cache[95] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"صرف و لم ينفذ\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مقبول دون صرف'\n    }]),\n    onClick: _cache[30] || (_cache[30] = $event => $options.selectFilter('مقبول دون صرف'))\n  }, [_createElementVNode(\"span\", _hoisted_46, _toDisplayString($data.myActivities.filter(a => a.state === 'مقبول دون صرف').length), 1 /* TEXT */), _cache[96] || (_cache[96] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مقبول دون صرف\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"stat-item clickable\", {\n      selected: $data.selectedFilter === 'مرسل'\n    }]),\n    onClick: _cache[31] || (_cache[31] = $event => $options.selectFilter('مرسل'))\n  }, [_createElementVNode(\"span\", _hoisted_47, _toDisplayString($data.myActivities.filter(a => a.state === 'مرسل').length), 1 /* TEXT */), _cache[97] || (_cache[97] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"مرسل\", -1 /* CACHED */))], 2 /* CLASS */)])]), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"table\", _hoisted_49, [_cache[101] || (_cache[101] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", {\n    class: \"col-title\"\n  }, \"عنوان النشاط\"), _createElementVNode(\"th\", {\n    class: \"col-owner\"\n  }, \"صاحب النشاط\"), _createElementVNode(\"th\", {\n    class: \"col-date\"\n  }, \"تاريخ النشاط\"), _createElementVNode(\"th\", {\n    class: \"col-status\"\n  }, \"الحالة\"), _createElementVNode(\"th\", {\n    class: \"col-governorate\"\n  }, \"المحافظة\"), _createElementVNode(\"th\", {\n    class: \"col-coordinator\"\n  }, \"المنسق\"), _createElementVNode(\"th\", {\n    class: \"col-actions\"\n  }, \"الإجراءات\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredActivities, activity => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: activity.id,\n      class: \"activity-row clickable-row\",\n      onClick: $event => $options.openActivityModal(activity)\n    }, [_createElementVNode(\"td\", _hoisted_51, [_createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"h4\", null, _toDisplayString(activity.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_53, _toDisplayString(activity.short_description || 'لا يوجد وصف'), 1 /* TEXT */)])]), _createElementVNode(\"td\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [_createElementVNode(\"span\", null, _toDisplayString(activity.owner_name), 1 /* TEXT */)])]), _createElementVNode(\"td\", _hoisted_56, [_createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"span\", _hoisted_58, _toDisplayString(new Date(activity.activity_date).toLocaleDateString('ar-EG', {\n      day: 'numeric',\n      month: 'short'\n    })), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_59, _toDisplayString(new Date(activity.activity_date).getFullYear()), 1 /* TEXT */)])]), _createElementVNode(\"td\", _hoisted_60, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"status-badge\", $options.getStatusClass(activity.state)])\n    }, [_cache[98] || (_cache[98] = _createElementVNode(\"div\", {\n      class: \"status-indicator\"\n    }, null, -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString(activity.state), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_61, [_createElementVNode(\"span\", _hoisted_62, _toDisplayString(activity.submission_info.governorate), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_63, [_createElementVNode(\"span\", _hoisted_64, _toDisplayString(activity.submission_info.coordinator_name), 1 /* TEXT */)]), _createElementVNode(\"td\", {\n      class: \"col-actions\",\n      onClick: _cache[32] || (_cache[32] = _withModifiers(() => {}, [\"stop\"]))\n    }, [_createElementVNode(\"div\", _hoisted_65, [_createElementVNode(\"button\", {\n      onClick: $event => $options.editActivity(activity),\n      class: \"action-btn edit-btn\",\n      title: \"تعديل\"\n    }, [...(_cache[99] || (_cache[99] = [_createElementVNode(\"svg\", {\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"\n    })], -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_66), _createElementVNode(\"button\", {\n      onClick: $event => $options.deleteActivity(activity.id),\n      class: \"action-btn delete-btn\",\n      title: \"حذف\"\n    }, [...(_cache[100] || (_cache[100] = [_createElementVNode(\"svg\", {\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"\n    })], -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_67)])])], 8 /* PROPS */, _hoisted_50);\n  }), 128 /* KEYED_FRAGMENT */))])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" Account Settings Modal \"), $data.showAccountSettings ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"modal-overlay\",\n    onClick: _cache[42] || (_cache[42] = (...args) => $options.closeAccountSettings && $options.closeAccountSettings(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"modal-content account-settings-modal\",\n    onClick: _cache[41] || (_cache[41] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_68, [_cache[102] || (_cache[102] = _createElementVNode(\"h3\", null, \"إعدادات الحساب\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[33] || (_cache[33] = (...args) => $options.closeAccountSettings && $options.closeAccountSettings(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"form\", {\n    onSubmit: _cache[40] || (_cache[40] = _withModifiers((...args) => $options.updateAccountSettings && $options.updateAccountSettings(...args), [\"prevent\"])),\n    class: \"account-form\"\n  }, [_createElementVNode(\"div\", _hoisted_69, [_cache[103] || (_cache[103] = _createElementVNode(\"label\", {\n    for: \"fullName\"\n  }, \"الاسم الكامل:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"fullName\",\n    \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.accountForm.fullName = $event),\n    required: \"\",\n    class: \"form-input\",\n    placeholder: \"أدخل اسمك الكامل\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.fullName]])]), _createElementVNode(\"div\", _hoisted_70, [_cache[104] || (_cache[104] = _createElementVNode(\"label\", {\n    for: \"teamPin\"\n  }, \"رمز الفريق الرقمي:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"teamPin\",\n    \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.accountForm.teamPin = $event),\n    class: \"form-input\",\n    placeholder: \"أدخل رمز الفريق الرقمي\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.teamPin]])]), _createElementVNode(\"div\", _hoisted_71, [_cache[108] || (_cache[108] = _createElementVNode(\"h4\", null, \"تغيير كلمة المرور (اختياري)\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_72, [_cache[105] || (_cache[105] = _createElementVNode(\"label\", {\n    for: \"currentPassword\"\n  }, \"كلمة المرور الحالية:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"currentPassword\",\n    \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.accountForm.currentPassword = $event),\n    class: \"form-input\",\n    placeholder: \"أدخل كلمة المرور الحالية\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.currentPassword]])]), _createElementVNode(\"div\", _hoisted_73, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n    for: \"newPassword\"\n  }, \"كلمة المرور الجديدة:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"newPassword\",\n    \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.accountForm.newPassword = $event),\n    class: \"form-input\",\n    placeholder: \"أدخل كلمة المرور الجديدة (6 أحرف على الأقل)\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.newPassword]])]), _createElementVNode(\"div\", _hoisted_74, [_cache[107] || (_cache[107] = _createElementVNode(\"label\", {\n    for: \"confirmPassword\"\n  }, \"تأكيد كلمة المرور الجديدة:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"confirmPassword\",\n    \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.accountForm.confirmPassword = $event),\n    class: \"form-input\",\n    placeholder: \"أعد إدخال كلمة المرور الجديدة\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountForm.confirmPassword]])])]), _createElementVNode(\"div\", _hoisted_75, [_createElementVNode(\"button\", {\n    type: \"button\",\n    onClick: _cache[39] || (_cache[39] = (...args) => $options.closeAccountSettings && $options.closeAccountSettings(...args)),\n    class: \"cancel-btn\"\n  }, \"إلغاء\"), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.updatingAccount,\n    class: \"save-btn\"\n  }, [$data.updatingAccount ? (_openBlock(), _createElementBlock(\"span\", _hoisted_77, \"جاري الحفظ...\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_78, \"حفظ التغييرات\"))], 8 /* PROPS */, _hoisted_76)])], 32 /* NEED_HYDRATION */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" PIN Confirmation Modal \"), $data.showPinConfirmation ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"modal-overlay\",\n    onClick: _cache[49] || (_cache[49] = (...args) => $options.closePinConfirmation && $options.closePinConfirmation(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"modal-content pin-modal\",\n    onClick: _cache[48] || (_cache[48] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_79, [_cache[109] || (_cache[109] = _createElementVNode(\"h3\", null, \"تأكيد العملية\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[43] || (_cache[43] = (...args) => $options.closePinConfirmation && $options.closePinConfirmation(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"div\", _hoisted_80, [_createElementVNode(\"p\", _hoisted_81, _toDisplayString($data.pinConfirmationData.action === 'delete' ? 'لحذف هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' : 'لتعديل هذا النشاط، يرجى إدخال رمز الفريق الرقمي:'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_82, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n    for: \"confirmPin\"\n  }, \"رمز الفريق الرقمي:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"confirmPin\",\n    \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.pinConfirmationData.pin = $event),\n    placeholder: \"أدخل رمز الفريق الرقمي\",\n    onKeyup: _cache[45] || (_cache[45] = _withKeys((...args) => $options.confirmPinAction && $options.confirmPinAction(...args), [\"enter\"])),\n    class: \"form-control\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.pinConfirmationData.pin]])])]), _createElementVNode(\"div\", _hoisted_83, [_createElementVNode(\"button\", {\n    onClick: _cache[46] || (_cache[46] = (...args) => $options.closePinConfirmation && $options.closePinConfirmation(...args)),\n    class: \"cancel-btn\"\n  }, \"إلغاء\"), _createElementVNode(\"button\", {\n    onClick: _cache[47] || (_cache[47] = (...args) => $options.confirmPinAction && $options.confirmPinAction(...args)),\n    class: \"confirm-btn\",\n    disabled: !$data.pinConfirmationData.pin\n  }, _toDisplayString($data.pinConfirmationData.action === 'delete' ? 'حذف' : 'تعديل'), 9 /* TEXT, PROPS */, _hoisted_84)])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Activity Details Modal \"), $data.showActivityModal && $data.selectedActivity ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 2,\n    class: \"modal-overlay\",\n    onClick: _cache[55] || (_cache[55] = (...args) => $options.closeActivityModal && $options.closeActivityModal(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"modal-content activity-details-modal\",\n    onClick: _cache[54] || (_cache[54] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_85, [_cache[111] || (_cache[111] = _createElementVNode(\"h3\", null, \"تفاصيل النشاط\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[50] || (_cache[50] = (...args) => $options.closeActivityModal && $options.closeActivityModal(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"div\", _hoisted_86, [_createCommentVNode(\" Document Header \"), _createElementVNode(\"div\", _hoisted_87, [_cache[112] || (_cache[112] = _createStaticVNode(\"<div class=\\\"logo-section\\\"><div class=\\\"logo-container\\\"><img src=\\\"\" + _imports_1 + \"\\\" alt=\\\"شعار المجلس الأعلى للشباب\\\" class=\\\"organization-logo\\\"><span class=\\\"logo-label\\\">شعار المجلس</span></div><div class=\\\"organization-info\\\"><h2>المجلس الأعلى للشباب</h2><h3>الفريق الوطني للشباب الرقمي</h3></div><div class=\\\"logo-container\\\"><img src=\\\"\" + _imports_2 + \"\\\" alt=\\\"شعار الفريق الوطني للشباب الرقمي\\\" class=\\\"organization-logo\\\"><span class=\\\"logo-label\\\">شعار الفريق</span></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_88, [_createElementVNode(\"h1\", null, _toDisplayString($data.selectedActivity.title), 1 /* TEXT */)])]), _createCommentVNode(\" Activity Details Table \"), _createElementVNode(\"div\", _hoisted_89, [_createElementVNode(\"div\", _hoisted_90, [_cache[113] || (_cache[113] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"الاسم\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_91, \"الفريق الوطني للشباب الرقمي - \" + _toDisplayString($data.selectedActivity.governorate) + \" - \" + _toDisplayString($data.selectedActivity.owner_name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_92, [_cache[114] || (_cache[114] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"اسم النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_93, _toDisplayString($data.selectedActivity.title), 1 /* TEXT */)]), $data.selectedActivity.activity_idea ? (_openBlock(), _createElementBlock(\"div\", _hoisted_94, [_cache[115] || (_cache[115] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"فكرة النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_95, _toDisplayString($data.selectedActivity.activity_idea), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.activity_goals && $data.selectedActivity.activity_goals.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_96, [_cache[116] || (_cache[116] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"أهداف النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_97, [_createElementVNode(\"ul\", _hoisted_98, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.selectedActivity.activity_goals, goal => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: goal.text\n    }, \"- \" + _toDisplayString(goal.text), 1 /* TEXT */);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.target_groups && $data.selectedActivity.target_groups.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_99, [_cache[117] || (_cache[117] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"الفئة المستهدفة\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_100, [_createElementVNode(\"ul\", _hoisted_101, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.selectedActivity.target_groups, group => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: group.text\n    }, _toDisplayString(group.text), 1 /* TEXT */);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.audience_count ? (_openBlock(), _createElementBlock(\"div\", _hoisted_102, [_cache[118] || (_cache[118] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"عدد المستهدفين\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_103, _toDisplayString($data.selectedActivity.audience_count) + \" شخص\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_104, [_cache[119] || (_cache[119] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"تاريخ النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_105, _toDisplayString($options.formatDate($data.selectedActivity.activity_date)), 1 /* TEXT */)]), $data.selectedActivity.activity_time ? (_openBlock(), _createElementBlock(\"div\", _hoisted_106, [_cache[120] || (_cache[120] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"وقت النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_107, _toDisplayString($options.formatTime($data.selectedActivity.activity_time)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.activity_location ? (_openBlock(), _createElementBlock(\"div\", _hoisted_108, [_cache[121] || (_cache[121] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"مكان النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_109, _toDisplayString($data.selectedActivity.activity_location), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.activity_duration ? (_openBlock(), _createElementBlock(\"div\", _hoisted_110, [_cache[122] || (_cache[122] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"مدة النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_111, _toDisplayString($data.selectedActivity.activity_duration), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.activity_budget ? (_openBlock(), _createElementBlock(\"div\", _hoisted_112, [_cache[123] || (_cache[123] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"ميزانية النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_113, _toDisplayString($options.formatCurrency($data.selectedActivity.activity_budget)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.budget_details && $data.selectedActivity.budget_details.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_114, [_cache[126] || (_cache[126] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"تفاصيل الصرف\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_115, [_createElementVNode(\"table\", _hoisted_116, [_cache[125] || (_cache[125] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"المادة\"), _createElementVNode(\"th\", null, \"النوع\"), _createElementVNode(\"th\", null, \"العدد\"), _createElementVNode(\"th\", null, \"السعر\"), _createElementVNode(\"th\", null, \"المجموع\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.selectedActivity.budget_details, detail => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: detail.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(detail.name), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(detail.type), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(detail.amount), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString($options.formatCurrency(detail.price)), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString($options.formatCurrency(detail.budgetPrice)), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))]), $data.selectedActivity.total_budget ? (_openBlock(), _createElementBlock(\"tfoot\", _hoisted_117, [_createElementVNode(\"tr\", _hoisted_118, [_cache[124] || (_cache[124] = _createElementVNode(\"td\", {\n    colspan: \"4\"\n  }, [_createElementVNode(\"strong\", null, \"الإجمالي\")], -1 /* CACHED */)), _createElementVNode(\"td\", null, [_createElementVNode(\"strong\", null, _toDisplayString($options.formatCurrency($data.selectedActivity.total_budget)), 1 /* TEXT */)])])])) : _createCommentVNode(\"v-if\", true)])])])) : _createCommentVNode(\"v-if\", true), $data.selectedActivity.activity_levels && $data.selectedActivity.activity_levels.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_119, [_cache[127] || (_cache[127] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"مراحل تنفيذ النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_120, [_createElementVNode(\"ul\", _hoisted_121, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.selectedActivity.activity_levels, level => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: level.description\n    }, _toDisplayString(level.description), 1 /* TEXT */);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_122, [_cache[128] || (_cache[128] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"حالة النشاط\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_123, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"status-badge\", $options.getStatusClass($data.selectedActivity.state)])\n  }, _toDisplayString($data.selectedActivity.state), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_124, [_cache[129] || (_cache[129] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"المحافظة\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_125, _toDisplayString($data.selectedActivity.governorate), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_126, [_cache[130] || (_cache[130] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"منسق المحافظة\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_127, _toDisplayString($data.selectedActivity.coordinator_name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_128, [_cache[131] || (_cache[131] = _createElementVNode(\"div\", {\n    class: \"detail-label\"\n  }, \"تاريخ الإرسال\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_129, _toDisplayString($options.formatDate($data.selectedActivity.created_at)), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_130, [_createElementVNode(\"button\", {\n    onClick: _cache[51] || (_cache[51] = (...args) => $options.printActivity && $options.printActivity(...args)),\n    class: \"print-btn\"\n  }, [(_openBlock(), _createElementBlock(\"svg\", _hoisted_131, [...(_cache[132] || (_cache[132] = [_createElementVNode(\"path\", {\n    d: \"M18,3H6V7H18M19,12A1,1 0 0,1 18,11A1,1 0 0,1 19,10A1,1 0 0,1 20,11A1,1 0 0,1 19,12M16,19H8V14H16M19,8H5A3,3 0 0,0 2,11V17H6V21H18V17H22V11A3,3 0 0,0 19,8Z\"\n  }, null, -1 /* CACHED */)]))])), _cache[133] || (_cache[133] = _createTextVNode(\" طباعة \", -1 /* CACHED */))]), _createElementVNode(\"button\", {\n    onClick: _cache[52] || (_cache[52] = (...args) => $options.exportToDocx && $options.exportToDocx(...args)),\n    class: \"export-docx-btn\"\n  }, [(_openBlock(), _createElementBlock(\"svg\", _hoisted_132, [...(_cache[134] || (_cache[134] = [_createElementVNode(\"path\", {\n    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n  }, null, -1 /* CACHED */)]))])), _cache[135] || (_cache[135] = _createTextVNode(\" تصدير كملف Word \", -1 /* CACHED */))]), _createElementVNode(\"button\", {\n    onClick: _cache[53] || (_cache[53] = (...args) => $options.closeActivityModal && $options.closeActivityModal(...args)),\n    class: \"close-modal-btn\"\n  }, \" إغلاق \")])])])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "d", "width", "height", "viewBox", "fill", "style", "_createElementVNode", "_hoisted_1", "_hoisted_2", "src", "alt", "_hoisted_3", "_normalizeClass", "active", "$data", "current<PERSON>iew", "onClick", "_cache", "$event", "$options", "set<PERSON><PERSON><PERSON>View", "_hoisted_4", "args", "toggleUserMenu", "_hoisted_5", "_hoisted_6", "_toDisplayString", "user", "full_name", "username", "showUserMenu", "_createElementBlock", "_hoisted_7", "openAccountSettings", "rank", "goToAdmin", "logout", "_hoisted_8", "_createCommentVNode", "_hoisted_9", "_hoisted_10", "for", "type", "id", "placeholder", "<PERSON><PERSON><PERSON>", "disabled", "required", "_hoisted_11", "AddActivityItem", "submitCV", "_hoisted_12", "_hoisted_13", "_hoisted_14", "exportToCSV", "loadingActivities", "filteredActivities", "length", "title", "refreshActivities", "_hoisted_17", "_hoisted_18", "myActivities", "_hoisted_19", "editingActivity", "cancelEdit", "_withModifiers", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "owner_name", "_hoisted_24", "_hoisted_25", "_hoisted_26", "activity_date", "_hoisted_27", "state", "_hoisted_28", "_hoisted_29", "short_description", "rows", "_hoisted_30", "saveActivity", "_Fragment", "key", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "searchQuery", "handleSearchIconClick", "_hoisted_36", "_hoisted_37", "_hoisted_38", "selected", "<PERSON><PERSON><PERSON><PERSON>", "selectFilter", "_hoisted_39", "_hoisted_40", "filter", "a", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_renderList", "activity", "openActivityModal", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "Date", "toLocaleDateString", "day", "month", "_hoisted_59", "getFullYear", "_hoisted_60", "getStatusClass", "_hoisted_61", "_hoisted_62", "submission_info", "governorate", "_hoisted_63", "_hoisted_64", "coordinator_name", "_hoisted_65", "editActivity", "deleteActivity", "showAccountSettings", "closeAccountSettings", "_hoisted_68", "onSubmit", "updateAccountSettings", "_hoisted_69", "accountForm", "fullName", "_hoisted_70", "teamPin", "_hoisted_71", "_hoisted_72", "currentPassword", "_hoisted_73", "newPassword", "_hoisted_74", "confirmPassword", "_hoisted_75", "updatingAccount", "_hoisted_77", "_hoisted_78", "showPinConfirmation", "closePinConfirmation", "_hoisted_79", "_hoisted_80", "_hoisted_81", "pinConfirmationData", "action", "_hoisted_82", "pin", "onKeyup", "_with<PERSON><PERSON><PERSON>", "confirmPinAction", "_hoisted_83", "_hoisted_84", "showActivityModal", "selectedActivity", "closeActivityModal", "_hoisted_85", "_hoisted_86", "_hoisted_87", "_hoisted_88", "_hoisted_89", "_hoisted_90", "_hoisted_91", "_hoisted_92", "_hoisted_93", "activity_idea", "_hoisted_94", "_hoisted_95", "activity_goals", "_hoisted_96", "_hoisted_97", "_hoisted_98", "goal", "text", "target_groups", "_hoisted_99", "_hoisted_100", "_hoisted_101", "group", "audience_count", "_hoisted_102", "_hoisted_103", "_hoisted_104", "_hoisted_105", "formatDate", "activity_time", "_hoisted_106", "_hoisted_107", "formatTime", "activity_location", "_hoisted_108", "_hoisted_109", "activity_duration", "_hoisted_110", "_hoisted_111", "activity_budget", "_hoisted_112", "_hoisted_113", "formatCurrency", "budget_details", "_hoisted_114", "_hoisted_115", "_hoisted_116", "detail", "name", "amount", "price", "budgetPrice", "total_budget", "_hoisted_117", "_hoisted_118", "colspan", "activity_levels", "_hoisted_119", "_hoisted_120", "_hoisted_121", "level", "description", "_hoisted_122", "_hoisted_123", "_hoisted_124", "_hoisted_125", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_129", "created_at", "_hoisted_130", "printActivity", "_hoisted_131", "exportToDocx", "_hoisted_132"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\ActivityView.vue"], "sourcesContent": ["<script>\r\nimport { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell, WidthType, AlignmentType, BorderStyle, ImageRun } from 'docx';\r\nimport { saveAs } from 'file-saver';\r\n\r\nexport default {\r\n    name: 'ActivityView',\r\n    data() {\r\n        return {\r\n            activityOwnerName: '',\r\n            activityTitleName: '',\r\n            activityDescription: '',\r\n            activityDateInput: '',\r\n            activityIdeaInput: '',\r\n            activityGoals: [],\r\n            targetGroups: [],\r\n            audienceCount: 0,\r\n            activityLocationInput: '',\r\n            activityTimeInput: '',\r\n            activityDurationInput: '',\r\n            activityBudgetInput: 0,\r\n            budgetDetailsList: [],\r\n            activityTotalBudget: 0,\r\n            activityLevels: [],\r\n            activityStatus: '',\r\n            filledActivity: {\r\n                title: this.activityTitleName,\r\n                owner: this.activityOwnerName,\r\n                description: this.activityDescription,\r\n                date: this.activityDateInput,\r\n                idea: this.activityIdeaInput,\r\n                goals: this.activityGoals,\r\n                audience: this.targetGroups,\r\n                location: this.activityLocationInput,\r\n                time: this.activityTimeInput,\r\n                duration: this.activityDurationInput,\r\n                budget: this.activityBudgetInput,\r\n                budgetDetails: this.budgetDetailsList,\r\n                totalBudget: this.activityTotalBudget\r\n            },\r\n            //------------------------------------\r\n            activities: [],\r\n            //------------------------------------\r\n            user: null,\r\n            showUserMenu: false,\r\n            myActivities: [],\r\n            loadingActivities: false,\r\n            editingActivity: null,\r\n            showMyActivities: false,\r\n            currentView: 'submit',\r\n            coordinatorName: '',\r\n            selectedFilter: null, // null means show all, otherwise filter by state\r\n            searchQuery: '', // Search query for activity name and owner\r\n            showAccountSettings: false,\r\n            accountForm: {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            },\r\n            updatingAccount: false,\r\n            showPinConfirmation: false,\r\n            pinConfirmationData: {\r\n                pin: '',\r\n                action: '', // 'edit' or 'delete'\r\n                activity: null,\r\n                callback: null\r\n            },\r\n            showActivityModal: false,\r\n            selectedActivity: null\r\n        };\r\n    },\r\n    computed: {\r\n        filteredActivities() {\r\n            let filtered = this.myActivities;\r\n\r\n            // Filter by status if selected\r\n            if (this.selectedFilter) {\r\n                filtered = filtered.filter(activity => activity.state === this.selectedFilter);\r\n            }\r\n\r\n            // Filter by search query (activity name and owner)\r\n            if (this.searchQuery.trim()) {\r\n                const query = this.searchQuery.trim().toLowerCase();\r\n                filtered = filtered.filter(activity => {\r\n                    const title = (activity.title || '').toLowerCase();\r\n                    const owner = (activity.owner_name || '').toLowerCase();\r\n                    return title.includes(query) || owner.includes(query);\r\n                });\r\n            }\r\n\r\n            return filtered;\r\n        }\r\n    },\r\n    methods: {\r\n        selectFilter(state) {\r\n            // Toggle filter: if same state is clicked, clear filter; otherwise set new filter\r\n            this.selectedFilter = this.selectedFilter === state ? null : state;\r\n        },\r\n        clearSearch() {\r\n            this.searchQuery = '';\r\n        },\r\n        handleSearchIconClick() {\r\n            if (this.searchQuery) {\r\n                this.searchQuery = '';\r\n            }\r\n        },\r\n        toggleUserMenu() {\r\n            this.showUserMenu = !this.showUserMenu;\r\n        },\r\n        logout() {\r\n            localStorage.removeItem('ndyt_token');\r\n            localStorage.removeItem('ndyt_user');\r\n            localStorage.removeItem('ndyt_team_pin');\r\n            this.$router.push('/login');\r\n        },\r\n        goToAdmin() {\r\n            this.showUserMenu = false;\r\n            this.$router.push('/admin');\r\n        },\r\n        openAccountSettings() {\r\n            this.showUserMenu = false;\r\n            this.accountForm.fullName = this.user?.full_name || '';\r\n            this.accountForm.teamPin = localStorage.getItem('ndyt_team_pin') || '';\r\n            this.accountForm.currentPassword = '';\r\n            this.accountForm.newPassword = '';\r\n            this.accountForm.confirmPassword = '';\r\n            this.showAccountSettings = true;\r\n        },\r\n        closeAccountSettings() {\r\n            this.showAccountSettings = false;\r\n            this.accountForm = {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            };\r\n        },\r\n        async updateAccountSettings() {\r\n            // Validate form\r\n            if (!this.accountForm.fullName.trim()) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'يرجى إدخال الاسم الكامل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword !== this.accountForm.confirmPassword) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتان',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword.length < 6) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            this.updatingAccount = true;\r\n\r\n            try {\r\n                const updateData = {\r\n                    full_name: this.accountForm.fullName.trim(),\r\n                    team_pin: this.accountForm.teamPin.trim()\r\n                };\r\n\r\n                // Only include password if user wants to change it\r\n                if (this.accountForm.newPassword) {\r\n                    updateData.current_password = this.accountForm.currentPassword;\r\n                    updateData.new_password = this.accountForm.newPassword;\r\n                }\r\n\r\n                const response = await this.$api.put('/user/update-profile', updateData);\r\n\r\n                if (response.ok) {\r\n                    await response.json();\r\n\r\n                    // Update local user data\r\n                    this.user.full_name = this.accountForm.fullName.trim();\r\n                    localStorage.setItem('ndyt_user', JSON.stringify(this.user));\r\n                    localStorage.setItem('ndyt_team_pin', this.accountForm.teamPin.trim());\r\n\r\n                    await this.$swal.fire({\r\n                        title: 'تم التحديث بنجاح',\r\n                        text: 'تم تحديث معلومات الحساب بنجاح',\r\n                        icon: 'success'\r\n                    });\r\n\r\n                    this.closeAccountSettings();\r\n                } else {\r\n                    const errorData = await response.json();\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في التحديث',\r\n                        text: errorData.error || 'فشل في تحديث معلومات الحساب',\r\n                        icon: 'error'\r\n                    });\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error updating account:', error);\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في الاتصال',\r\n                    text: 'حدث خطأ أثناء تحديث معلومات الحساب',\r\n                    icon: 'error'\r\n                });\r\n            } finally {\r\n                this.updatingAccount = false;\r\n            }\r\n        },\r\n        async submitCV() {\r\n            try {\r\n                // Validate coordinator name\r\n                if (!this.coordinatorName.trim()) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إدخال اسم منسق المحافظة',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                // Collect all activity items\r\n                const activityItems = document.querySelectorAll('.activity-item');\r\n                \r\n                if (activityItems.length === 0) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إضافة نشاط واحد على الأقل',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                // Check if user is logged in (token will be handled by API service)\r\n                const token = localStorage.getItem('ndyt_token');\r\n                if (!token) {\r\n                    this.$toast.error('يرجى تسجيل الدخول أولاً');\r\n                    this.$router.push('/login');\r\n                    return;\r\n                }\r\n                \r\n                const activities = [];\r\n                let hasErrors = false;\r\n                \r\n                // Process each activity item\r\n                for (let index = 0; index < activityItems.length; index++) {\r\n                    const item = activityItems[index];\r\n\r\n                    // Extract basic fields - be more specific about selectors\r\n                    const ownerInput = item.querySelector('input[placeholder=\"اسم صاحب االنشاط\"]');\r\n                    const titleInput = item.querySelector('input[placeholder=\"عنوان النشاط\"]');\r\n                    const shortDescInput = item.querySelector('input[placeholder=\"وصف قصير للنشاط\"]');\r\n                    const dateInput = item.querySelector('input[type=\"date\"]');\r\n                    const stateSelect = item.querySelector('select.activity-input');\r\n\r\n                    const ownerName = ownerInput?.value?.trim();\r\n                    const title = titleInput?.value?.trim();\r\n                    const shortDescription = shortDescInput?.value?.trim();\r\n                    const activityDate = dateInput?.value;\r\n                    const state = stateSelect?.value;\r\n\r\n                    // Debug logging to verify state extraction\r\n                    console.log(`Activity ${index + 1} - State extracted:`, state, 'from element:', stateSelect);\r\n\r\n                    // Extract additional fields by finding specific elements\r\n                    const ideaTextarea = item.querySelector('textarea');\r\n                    const activityIdea = ideaTextarea?.value?.trim() || '';\r\n\r\n                    // Extract goals\r\n                    const goalInputs = item.querySelectorAll('ul li input[placeholder=\"هدف النشاط\"]');\r\n                    const activityGoals = Array.from(goalInputs).map(input => ({ text: input.value.trim() })).filter(goal => goal.text);\r\n\r\n                    // Extract target groups\r\n                    const targetGroupInputs = item.querySelectorAll('ul li input[placeholder=\"الفئة المستهدفة\"]');\r\n                    const targetGroups = Array.from(targetGroupInputs).map(input => ({ text: input.value.trim() })).filter(group => group.text);\r\n\r\n                    // Extract audience count\r\n                    const audienceInput = item.querySelector('input[type=\"stat-number\"]');\r\n                    const audienceCount = audienceInput?.value ? parseInt(audienceInput.value) : null;\r\n\r\n                    // Extract location, time, duration\r\n                    const locationInput = item.querySelector('input[placeholder=\"موقع النشاط\"]');\r\n                    const activityLocation = locationInput?.value?.trim() || '';\r\n\r\n                    const timeInput = item.querySelector('input[type=\"time\"]');\r\n                    const activityTime = timeInput?.value || null;\r\n\r\n                    const durationInput = item.querySelector('input[placeholder=\"مدة النشاط\"]');\r\n                    const activityDuration = durationInput?.value?.trim() || '';\r\n\r\n                    // Extract budget information\r\n                    const budgetInputs = item.querySelectorAll('.budget-input input');\r\n                    const activityBudget = budgetInputs[0]?.value ? parseFloat(budgetInputs[0].value) : null;\r\n                    const totalBudget = budgetInputs[1]?.value ? parseFloat(budgetInputs[1].value) : null;\r\n\r\n                    // Extract budget details using the specific class\r\n                    const budgetDetailItems = item.querySelectorAll('.budget-detail-item');\r\n                    const budgetDetails = [];\r\n\r\n                    budgetDetailItems.forEach(detailItem => {\r\n                        // Each budget detail item has multiple rows with inputs\r\n                        const allInputs = detailItem.querySelectorAll('input');\r\n                        if (allInputs.length >= 5) {\r\n                            const detail = {\r\n                                name: allInputs[0]?.value?.trim() || '',\r\n                                type: allInputs[1]?.value?.trim() || '',\r\n                                amount: allInputs[2]?.value ? parseInt(allInputs[2].value) : 0,\r\n                                price: allInputs[3]?.value ? parseFloat(allInputs[3].value) : 0,\r\n                                budgetPrice: allInputs[4]?.value ? parseFloat(allInputs[4].value) : 0\r\n                            };\r\n                            if (detail.name) budgetDetails.push(detail);\r\n                        }\r\n                    });\r\n\r\n                    // Debug logging for budget details\r\n                    console.log(`Activity ${index + 1} - Budget details extracted:`, budgetDetails);\r\n\r\n                    // Extract activity levels\r\n                    const levelInputs = item.querySelectorAll('ul li input[placeholder=\"وصف المرحلة التنفيذية\"]');\r\n                    const activityLevels = Array.from(levelInputs).map(input => ({ description: input.value.trim() })).filter(level => level.description);\r\n\r\n                    // Validate required fields\r\n                    if (!ownerName || !title || !activityDate || !state) {\r\n                        let missingFields = [];\r\n                        if (!ownerName) missingFields.push('اسم صاحب النشاط');\r\n                        if (!title) missingFields.push('عنوان النشاط');\r\n                        if (!activityDate) missingFields.push('تاريخ النشاط');\r\n                        if (!state) missingFields.push('حالة النشاط');\r\n\r\n                        this.$swal.fire({\r\n                            title: 'خطأ في البيانات',\r\n                            text: `يرجى ملء الحقول المطلوبة للنشاط رقم ${index + 1}: ${missingFields.join(', ')}`,\r\n                            icon: 'error'\r\n                        });\r\n                        hasErrors = true;\r\n                        break;\r\n                    }\r\n\r\n                    let fileId = null;\r\n\r\n                    // Handle file upload if a file is selected\r\n                    if (item.selectedFile) {\r\n                        try {\r\n                            const uploadResponse = await this.$api.uploadFile(item.selectedFile);\r\n\r\n                            if (uploadResponse.ok) {\r\n                                const uploadResult = await uploadResponse.json();\r\n                                if (uploadResult.file && uploadResult.file.id) {\r\n                                    fileId = uploadResult.file.id;\r\n                                }\r\n                            } else {\r\n                                const errorData = await uploadResponse.json();\r\n                                this.$toast.error(`فشل في رفع ملف النشاط رقم ${index + 1}: ${errorData.error || 'خطأ غير معروف'}`);\r\n                                hasErrors = true;\r\n                                break;\r\n                            }\r\n                        } catch (uploadError) {\r\n                            if (uploadError.message === 'Session expired') {\r\n                                // Token expiration is already handled by the API service\r\n                                return;\r\n                            }\r\n                            console.error('File upload error:', uploadError);\r\n                            this.$toast.error(`خطأ في رفع ملف النشاط رقم ${index + 1}`);\r\n                            hasErrors = true;\r\n                            break;\r\n                        }\r\n                    }\r\n\r\n                    activities.push({\r\n                        owner_name: ownerName,\r\n                        title: title,\r\n                        short_description: shortDescription || '',\r\n                        activity_date: activityDate,\r\n                        state: state,\r\n                        file_id: fileId,\r\n                        activity_idea: activityIdea,\r\n                        activity_goals: activityGoals,\r\n                        target_groups: targetGroups,\r\n                        audience_count: audienceCount,\r\n                        activity_location: activityLocation,\r\n                        activity_time: activityTime,\r\n                        activity_duration: activityDuration,\r\n                        activity_budget: activityBudget,\r\n                        budget_details: budgetDetails,\r\n                        total_budget: totalBudget,\r\n                        activity_levels: activityLevels\r\n                    });\r\n                }\r\n                \r\n                if (hasErrors) return;\r\n                \r\n                // Prepare submission data\r\n                const submissionData = {\r\n                    coordinator_name: this.coordinatorName.trim(),\r\n                    activities: activities\r\n                };\r\n                \r\n                // Submit to backend\r\n                const response = await this.$api.post('/submissions', submissionData);\r\n\r\n                if (response.ok) {\r\n                    await response.json();\r\n                    this.$toast.success('تم إرسال النشاطات بنجاح!');\r\n\r\n                    // Clear the form\r\n                    this.coordinatorName = '';\r\n\r\n                    // Remove all activity items\r\n                    activityItems.forEach(item => item.remove());\r\n\r\n                    // Refresh my activities if they're currently shown\r\n                    if (this.showMyActivities) {\r\n                        this.fetchMyActivities();\r\n                    }\r\n                } else {\r\n                    let errorData = {};\r\n                    try {\r\n                        errorData = await response.json();\r\n                    } catch (jsonError) {\r\n                        console.error('Error parsing response JSON:', jsonError);\r\n                    }\r\n                    this.$toast.error(`فشل في إرسال النشاطات: ${errorData.message || 'خطأ غير معروف'}`);\r\n                }\r\n                \r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error submitting activities:', error);\r\n                this.$toast.error('حدث خطأ أثناء إرسال النشاطات. يرجى المحاولة مرة أخرى.');\r\n            }\r\n        },\r\n        async fetchMyActivities() {\r\n            this.loadingActivities = true;\r\n            this.myActivities = []; // Clear existing activities\r\n\r\n            try {\r\n                const response = await this.$api.get('/activities');\r\n\r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n\r\n                    // The new endpoint returns activities directly with role-based filtering\r\n                    if (data.activities && Array.isArray(data.activities)) {\r\n                        this.myActivities = data.activities.map(activity => ({\r\n                            ...activity,\r\n                            submission_info: {\r\n                                id: activity.submission_id,\r\n                                governorate: activity.governorate,\r\n                                coordinator_name: activity.coordinator_name,\r\n                                created_at: activity.created_at\r\n                            }\r\n                        }));\r\n                    }\r\n                } else if (response.status === 404) {\r\n                    // No activities found - this is normal, not an error\r\n                    this.myActivities = [];\r\n                } else {\r\n                    const errorData = await response.json().catch(() => ({}));\r\n                    console.error('Failed to fetch activities:', errorData);\r\n                    this.$toast.error(`فشل في تحميل النشاطات: ${errorData.message || 'خطأ في الخادم'}`);\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error fetching activities:', error);\r\n                if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n                    this.$toast.error('خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');\r\n                } else {\r\n                    this.$toast.error('حدث خطأ أثناء تحميل النشاطات. يرجى المحاولة مرة أخرى.');\r\n                }\r\n            } finally {\r\n                this.loadingActivities = false;\r\n            }\r\n        },\r\n        toggleMyActivities() {\r\n            this.showMyActivities = !this.showMyActivities;\r\n            if (this.showMyActivities && this.myActivities.length === 0) {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        editActivity(activity) {\r\n            this.showPinConfirmationModal('edit', activity, () => {\r\n                this.openEditModal(activity);\r\n            });\r\n        },\r\n        openEditModal(activity) {\r\n            this.editingActivity = { ...activity };\r\n            // Ensure date is properly formatted for date input (YYYY-MM-DD)\r\n            if (this.editingActivity.activity_date) {\r\n                const date = new Date(this.editingActivity.activity_date);\r\n                // Use timezone-safe formatting to avoid date shifting\r\n                const year = date.getFullYear();\r\n                const month = String(date.getMonth() + 1).padStart(2, '0');\r\n                const day = String(date.getDate()).padStart(2, '0');\r\n                this.editingActivity.activity_date = `${year}-${month}-${day}`;\r\n            }\r\n        },\r\n        async saveActivity() {\r\n            if (!this.editingActivity) return;\r\n\r\n            try {\r\n                const updateData = {\r\n                    owner_name: this.editingActivity.owner_name,\r\n                    title: this.editingActivity.title,\r\n                    short_description: this.editingActivity.short_description,\r\n                    activity_date: this.editingActivity.activity_date,\r\n                    state: this.editingActivity.state\r\n                };\r\n\r\n                const response = await this.$api.put(`/activities/${this.editingActivity.id}`, updateData);\r\n\r\n                if (response.ok) {\r\n                    const updatedActivity = await response.json();\r\n                    // Update the activity in the list\r\n                    const index = this.myActivities.findIndex(a => a.id === updatedActivity.id);\r\n                    if (index !== -1) {\r\n                        this.myActivities[index] = { ...updatedActivity, submission_info: this.myActivities[index].submission_info };\r\n                    }\r\n                    this.editingActivity = null;\r\n                    this.$toast.success('تم تحديث النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في تحديث النشاط');\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error updating activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء تحديث النشاط');\r\n            }\r\n        },\r\n        cancelEdit() {\r\n            this.editingActivity = null;\r\n        },\r\n        deleteActivity(activityId) {\r\n            const activity = this.myActivities.find(a => a.id === activityId);\r\n            this.showPinConfirmationModal('delete', activity, async () => {\r\n                await this.performDeleteActivity(activityId);\r\n            });\r\n        },\r\n        async performDeleteActivity(activityId) {\r\n            try {\r\n                const response = await this.$api.delete(`/activities/${activityId}`);\r\n\r\n                if (response.ok) {\r\n                    this.myActivities = this.myActivities.filter(a => a.id !== activityId);\r\n                    this.$toast.success('تم حذف النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في حذف النشاط');\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error deleting activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء حذف النشاط');\r\n            }\r\n        },\r\n        getStatusClass(status) {\r\n            const statusMap = {\r\n                'منفذ بصرف': 'status-executed-paid',\r\n                'منفذ بدون صرف': 'status-executed-unpaid',\r\n                'مقبول': 'status-accepted',\r\n                'مرفوض': 'status-rejected',\r\n                'يحتاج تعديل': 'status-needs-edit',\r\n                'صرف و لم ينفذ': 'status-paid-not-executed',\r\n                'مقبول دون صرف': 'status-accepted-unpaid',\r\n                'مرسل': 'status-sent'\r\n            };\r\n            return statusMap[status] || 'status-default';\r\n        },\r\n        AddActivityItem() {\r\n            const activityDiv = document.createElement('div');\r\n            activityDiv.className = 'activity-item';\r\n            const activityOwner = document.createElement('input');\r\n            activityOwner.type = 'text';\r\n            activityOwner.placeholder = 'اسم صاحب االنشاط';\r\n            activityOwner.className = 'activity-input';\r\n            activityOwner.required = true;\r\n            activityOwner.addEventListener('input', (event) => {\r\n                this.activityOwnerName = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityOwner);\r\n            const activityTitle = document.createElement('input');\r\n            activityTitle.type = 'text';\r\n            activityTitle.placeholder = 'عنوان النشاط';\r\n            activityTitle.className = 'activity-input';\r\n            activityTitle.required = true;\r\n            activityTitle.addEventListener('input', (event) => {\r\n                this.activityTitle = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityTitle);\r\n            const activityShortDescription = document.createElement('input');\r\n            activityShortDescription.type = 'text';\r\n            activityShortDescription.placeholder = 'وصف قصير للنشاط';\r\n            activityShortDescription.className = 'activity-input';\r\n            activityShortDescription.required = true;\r\n            activityShortDescription.addEventListener('input', (event) => {\r\n                this.activityDescription = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityShortDescription);\r\n            const activityDateLabel = document.createElement('label');\r\n            activityDateLabel.textContent = 'تاريخ النشاط';\r\n            activityDiv.appendChild(activityDateLabel);\r\n            const activityDate = document.createElement('input');\r\n            activityDate.type = 'date';\r\n            activityDate.className = 'activity-input';\r\n            activityDate.required = true;\r\n            activityDate.addEventListener('input', (event) => {\r\n                this.activityDateInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityDate);\r\n            const activityIdeaLabel = document.createElement('label');\r\n            activityIdeaLabel.textContent = 'فكرة النشاط';\r\n            activityDiv.appendChild(activityIdeaLabel);\r\n            const activityIdea = document.createElement('textarea');\r\n            activityIdea.placeholder = 'فكرة النشاط';\r\n            activityIdea.className = 'activity-input';\r\n            activityIdea.required = true;\r\n            activityIdea.addEventListener('input', (event) => {\r\n                this.activityIdeaInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityIdea);\r\n            const activityGoalsLabel = document.createElement('label');\r\n            activityGoalsLabel.textContent = 'أهداف النشاط';\r\n            activityDiv.appendChild(activityGoalsLabel);\r\n            const activityGoalsList = document.createElement('ul');\r\n            activityDiv.appendChild(activityGoalsList);\r\n            const AddActivityGoal = () => {\r\n                const goal = { text: '' }\r\n                const goalItem = document.createElement('li');\r\n                const goalInput = document.createElement('input');\r\n                goalInput.type = 'text';\r\n                goalInput.placeholder = 'هدف النشاط';\r\n                goalInput.className = 'activity-input';\r\n                goalInput.required = true;\r\n                goalInput.addEventListener('input', (event) => {\r\n                    goal.text = event.target.value;\r\n                });\r\n                this.activityGoals.push(goal);\r\n                goalItem.appendChild(goalInput);\r\n                activityGoalsList.appendChild(goalItem);\r\n            };\r\n            const activityGoalsAddButton = document.createElement('button');\r\n            activityGoalsAddButton.textContent = 'إضافة هدف';\r\n            activityGoalsAddButton.onclick = AddActivityGoal;\r\n            activityDiv.appendChild(activityGoalsAddButton);\r\n            const activityTargetGroupLabel = document.createElement('label');\r\n            activityTargetGroupLabel.textContent = 'الفئة المستهدفة';\r\n            activityDiv.appendChild(activityTargetGroupLabel);\r\n            const activityTargetGroupList = document.createElement('ul');\r\n            activityDiv.appendChild(activityTargetGroupList);\r\n            const AddActivityTargetGroup = () => {\r\n                const targetGroup = { text: '' };\r\n                const targetGroupItem = document.createElement('li');\r\n                const targetGroupInput = document.createElement('input');\r\n                targetGroupInput.type = 'text';\r\n                targetGroupInput.placeholder = 'الفئة المستهدفة';\r\n                targetGroupInput.className = 'activity-input';\r\n                targetGroupInput.required = true;\r\n                targetGroupInput.addEventListener('input', (event) => {\r\n                    targetGroup.text = event.target.value;\r\n                });\r\n                this.targetGroups.push(targetGroup);\r\n                targetGroupItem.appendChild(targetGroupInput);\r\n                activityTargetGroupList.appendChild(targetGroupItem);\r\n            };\r\n            const activityTargetGroupAddButton = document.createElement('button');\r\n            activityTargetGroupAddButton.textContent = 'إضافة فئة مستهدفة';\r\n            activityTargetGroupAddButton.onclick = AddActivityTargetGroup;\r\n            activityDiv.appendChild(activityTargetGroupAddButton);\r\n            const activityAudienceCountLabel = document.createElement('label');\r\n            activityAudienceCountLabel.textContent = 'عدد الجمهور المستهدف';\r\n            activityDiv.appendChild(activityAudienceCountLabel);\r\n            const activityAudienceCountDiv = document.createElement('div');\r\n            activityAudienceCountDiv.style = 'display: flex; flex-direction: row; align-items: center; gap: 30px;';\r\n            const activityAudienceCount = document.createElement('input');\r\n            activityAudienceCount.type = 'stat-number';\r\n            activityAudienceCount.className = 'activity-input';\r\n            activityAudienceCount.required = true;\r\n            activityAudienceCount.addEventListener('input', (event) => {\r\n                this.audienceCount = event.target.value;\r\n            });\r\n            activityAudienceCountDiv.appendChild(activityAudienceCount);\r\n            const activityAudienceCountCurrency = document.createElement('span');\r\n            activityAudienceCountCurrency.textContent = 'شخص';\r\n            activityAudienceCountDiv.appendChild(activityAudienceCountCurrency);\r\n            activityDiv.appendChild(activityAudienceCountDiv);\r\n            const activityLocationLabel = document.createElement('label');\r\n            activityLocationLabel.textContent = 'موقع النشاط';\r\n            activityDiv.appendChild(activityLocationLabel);\r\n            const activityLocation = document.createElement('input');\r\n            activityLocation.type = 'text';\r\n            activityLocation.placeholder = 'موقع النشاط';\r\n            activityLocation.className = 'activity-input';\r\n            activityLocation.required = true;\r\n            activityLocation.addEventListener('input', (event) => {\r\n                this.activityLocationInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityLocation);\r\n            const activityTimeLabel = document.createElement('label');\r\n            activityTimeLabel.textContent = 'وقت النشاط';\r\n            activityDiv.appendChild(activityTimeLabel);\r\n            const activityTime = document.createElement('input');\r\n            activityTime.type = 'time';\r\n            activityTime.className = 'activity-input';\r\n            activityTime.required = true;\r\n            activityTime.addEventListener('input', (event) => {\r\n                this.activityTimeInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityTime);\r\n            const activityDurationLabel = document.createElement('label');\r\n            activityDurationLabel.textContent = 'مدة النشاط';\r\n            activityDiv.appendChild(activityDurationLabel);\r\n            const activityDuration = document.createElement('input');\r\n            activityDuration.type = 'text';\r\n            activityDuration.placeholder = 'مدة النشاط';\r\n            activityDuration.className = 'activity-input';\r\n            activityDuration.required = true;\r\n            activityDuration.addEventListener('input', (event) => {\r\n                this.activityDurationInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityDuration);\r\n            const activityBudgetLabel = document.createElement('label')\r\n            activityBudgetLabel.textContent = 'ميزانية النشاط'\r\n            activityDiv.appendChild(activityBudgetLabel)\r\n            const BudgetDiv = document.createElement('div');\r\n            BudgetDiv.className = 'budget-input';\r\n            BudgetDiv.style.display = 'flex';\r\n            BudgetDiv.style.flexDirection = 'row';\r\n            BudgetDiv.style.gap = '40px';\r\n            BudgetDiv.style.alignItems = 'center';\r\n            BudgetDiv.style.direction = 'rtl'; \r\n            const BudgetInput = document.createElement('input');\r\n            BudgetInput.type = 'stat-number'; \r\n            BudgetInput.placeholder = 'أدخل الميزانية'\r\n            BudgetInput.required = true;\r\n            BudgetInput.addEventListener('input', (event) => {\r\n                this.activityBudgetInput = event.target.value;\r\n            });\r\n            BudgetDiv.appendChild(BudgetInput);\r\n            const currencyTypeLabel = document.createElement('label');\r\n            currencyTypeLabel.textContent = 'د. ع'; \r\n            BudgetDiv.innerHTML += currencyTypeLabel.outerHTML;\r\n            activityDiv.appendChild(BudgetDiv);\r\n            const activityBudgetDetailsLabel = document.createElement('label');\r\n            activityBudgetDetailsLabel.textContent = 'تفاصيل الصرف';\r\n            activityDiv.appendChild(activityBudgetDetailsLabel);\r\n            const activityBudgetDetailsList = document.createElement('ul');\r\n            activityDiv.appendChild(activityBudgetDetailsList);\r\n            const AddActivityBudgetDetailsRecord = () => {\r\n               const material = { name: '', type: '', unit: '', amount: 0, price: 0, budgetPrice: 0 };\r\n               const budgetDetailItem = document.createElement('div');\r\n               budgetDetailItem.className = 'budget-detail-item';\r\n               budgetDetailItem.style =\r\n               'display: flex; flex-direction: column; gap: 20px;' +\r\n               'align-items: start; margin-bottom: 10px; justify-items: center;' +\r\n               'background-color: transparent; padding: 5px 25px; border-radius: 5px;' +\r\n               'border: 1px solid gray;';\r\n               const materialNameRow = document.createElement('div');\r\n               materialNameRow.style.display = 'flex'\r\n               materialNameRow.flexDirection = 'row'\r\n               materialNameRow.style.gap = '80px'\r\n               const materialNameLabel = document.createElement('label');\r\n               materialNameLabel.textContent = 'اسم المادة';\r\n               materialNameRow.appendChild(materialNameLabel);\r\n               const materialNameInput = document.createElement('input');\r\n               materialNameInput.type = 'text';\r\n               materialNameInput.placeholder = 'أدخل اسم المادة';\r\n               materialNameInput.required = true;\r\n               materialNameInput.addEventListener('input', (event) => {\r\n                   material.name = event.target.value;\r\n               });\r\n               materialNameRow.appendChild(materialNameInput);\r\n               budgetDetailItem.appendChild(materialNameRow);\r\n               const materialTypeRow = document.createElement('div')\r\n               materialTypeRow.style.display = 'flex'\r\n               materialTypeRow.style.flexDirection = 'row'\r\n               materialTypeRow.style.gap = '80px'\r\n               const materialTypeLabel = document.createElement('label')\r\n               materialTypeLabel.textContent = 'نوع المادة'\r\n               const materialTypeInput = document.createElement('input')\r\n               materialTypeInput.type = 'text'\r\n               materialTypeInput.placeholder = 'أدخل نوع المادة'\r\n               materialTypeInput.required = true;\r\n               materialTypeInput.addEventListener('input', (event) => {\r\n                   material.type = event.target.value;\r\n               });\r\n               materialTypeRow.appendChild(materialTypeLabel)\r\n               materialTypeRow.appendChild(materialTypeInput)\r\n               budgetDetailItem.appendChild(materialTypeRow)\r\n               const materialAmountRow = document.createElement('div')\r\n               materialAmountRow.style.display = 'flex'\r\n               materialAmountRow.style.flexDirection = 'row'\r\n               materialAmountRow.style.gap = '80px'\r\n               const materialAmountLabel = document.createElement('label')\r\n               materialAmountLabel.textContent = 'عدد الكمية'\r\n               materialAmountRow.appendChild(materialAmountLabel)\r\n               const materialAmountInput = document.createElement('input')\r\n               materialAmountInput.type = 'text'\r\n               materialAmountInput.placeholder = 'أدخل عدد الكمية'\r\n               materialAmountInput.required = true;\r\n               materialAmountInput.addEventListener('input', (event) => {\r\n                   material.amount = event.target.value;\r\n               });\r\n               materialAmountRow.appendChild(materialAmountInput)\r\n               budgetDetailItem.appendChild(materialAmountRow)\r\n               const materialPriceRow = document.createElement('div')\r\n               materialPriceRow.style.display = 'flex'\r\n               materialPriceRow.style.flexDirection = 'row'\r\n               materialPriceRow.style.gap = '80px'\r\n               const materialPriceLabel = document.createElement('label')\r\n               materialPriceLabel.textContent = 'سعر الوحدة'\r\n               materialPriceRow.appendChild(materialPriceLabel)\r\n               const materialPriceInput = document.createElement('input')\r\n               materialPriceInput.type = 'text'\r\n               materialPriceInput.placeholder= 'أدخل سعر المادة'\r\n               materialPriceInput.required = true;\r\n               materialPriceInput.addEventListener('input', (event) => {\r\n                   material.price = event.target.value;\r\n               });\r\n               materialPriceRow.appendChild(materialPriceInput)\r\n               budgetDetailItem.appendChild(materialPriceRow)\r\n               const materialBudgetPriceRow = document.createElement('div')\r\n               materialBudgetPriceRow.style.display = 'flex'\r\n               materialBudgetPriceRow.style.flexDirection = 'row'\r\n               materialBudgetPriceRow.style.gap = '80px'\r\n               const materialBudgetPriceLabel = document.createElement('label')\r\n               materialBudgetPriceLabel.textContent = 'سعر الصرف'\r\n               materialBudgetPriceRow.appendChild(materialBudgetPriceLabel)\r\n               const materialBudgetPriceInput = document.createElement('input')\r\n               materialBudgetPriceInput.type = 'text'\r\n               materialBudgetPriceInput.placeholder= 'أدخل سعر الصرف'\r\n               materialBudgetPriceInput.required = true;\r\n               materialBudgetPriceInput.addEventListener('input', (event) => {\r\n                   material.budgetPrice = event.target.value;\r\n               });\r\n               this.budgetDetailsList.push(material);\r\n               materialBudgetPriceRow.appendChild(materialBudgetPriceInput)\r\n               budgetDetailItem.appendChild(materialBudgetPriceRow)\r\n               activityBudgetDetailsList.appendChild(budgetDetailItem);\r\n            };\r\n            const AddActivityBudgetDetailsRecordButton = document.createElement('button');\r\n            AddActivityBudgetDetailsRecordButton.textContent = 'إضافة صرف'\r\n            AddActivityBudgetDetailsRecordButton.onclick = AddActivityBudgetDetailsRecord;\r\n            activityDiv.appendChild(AddActivityBudgetDetailsRecordButton);\r\n            const activityTotalBudgetLabel = document.createElement('label')\r\n            activityTotalBudgetLabel.textContent = 'مجموع الصرف الكلي'\r\n            activityDiv.appendChild(activityTotalBudgetLabel)\r\n            const totalBudgetDiv = document.createElement('div')\r\n            totalBudgetDiv.className = 'budget-input'\r\n            totalBudgetDiv.style = \r\n                'display: flex; flex-direction: row; gap: 40px; align-items: center;'\r\n            const totalBudgetInput = document.createElement('input')\r\n            totalBudgetInput.type = 'stat-number'\r\n            totalBudgetInput.placeholder = 'أدخل المجموع الكلي'\r\n            totalBudgetInput.required = true;\r\n            totalBudgetInput.addEventListener('input', (event) => {\r\n                this.activityTotalBudget = event.target.value;\r\n            });\r\n            totalBudgetInput.className = 'budget-input'\r\n            totalBudgetDiv.appendChild(totalBudgetInput)\r\n            totalBudgetDiv.appendChild(currencyTypeLabel)\r\n            activityDiv.appendChild(totalBudgetDiv)\r\n            const activityAppliedLevelsLabel = document.createElement('label')\r\n            activityAppliedLevelsLabel.textContent = 'مراحل تنفيذ النشاط'\r\n            activityDiv.appendChild(activityAppliedLevelsLabel)\r\n            const activityAppliedLevelsList = document.createElement('ul');\r\n            activityDiv.appendChild(activityAppliedLevelsList);\r\n            const AddActivityAppliedLevel = () => {\r\n                const appliedLevel = { description: '' };\r\n                const appliedLevelItem = document.createElement('li');\r\n                const appliedLevelInput = document.createElement('input');\r\n                appliedLevelInput.type = 'text';\r\n                appliedLevelInput.placeholder = 'وصف المرحلة التنفيذية';\r\n                appliedLevelInput.className = 'activity-input';\r\n                appliedLevelInput.required = true;\r\n                appliedLevelInput.addEventListener('input', (event) => {\r\n                    appliedLevel.description = event.target.value;\r\n                });\r\n                this.activityLevels.push(appliedLevel);\r\n                appliedLevelItem.appendChild(appliedLevelInput);\r\n                activityAppliedLevelsList.appendChild(appliedLevelItem);\r\n            };\r\n            const activityAppliedLevelAddButton = document.createElement('button');\r\n            activityAppliedLevelAddButton.textContent = 'إضافة مرحلة تنفيذ';\r\n            activityAppliedLevelAddButton.onclick = AddActivityAppliedLevel;\r\n            activityDiv.appendChild(activityAppliedLevelAddButton)\r\n            const activityStateLabel = document.createElement('label');\r\n            activityStateLabel.textContent = 'حالة النشاط';\r\n            activityDiv.appendChild(activityStateLabel);\r\n            const activityApplyState = document.createElement('select');\r\n            activityApplyState.className = 'activity-input';\r\n            activityApplyState.required = true;\r\n\r\n            // Add default option\r\n            const defaultOption = document.createElement('option');\r\n            defaultOption.value = '';\r\n            defaultOption.textContent = 'اختر حالة النشاط';\r\n            defaultOption.disabled = true;\r\n            defaultOption.selected = true;\r\n            activityApplyState.appendChild(defaultOption);\r\n\r\n            const states = [\r\n                'منفذ بصرف',  'منفذ بدون صرف', 'مقبول', 'مرفوض',\r\n                'يحتاج تعديل', 'صرف و لم ينفذ', 'مقبول دون صرف', 'مرسل'\r\n            ];\r\n            states.forEach(state => {\r\n                const option = document.createElement('option');\r\n                option.value = state;\r\n                option.textContent = state;\r\n                activityApplyState.appendChild(option);\r\n            });\r\n            activityApplyState.addEventListener('change', (event) => {\r\n                this.activityStatus = event.target.value;\r\n                // Update the filledActivity object when status changes\r\n                this.filledActivity = {\r\n                    title: this.activityTitleName,\r\n                    owner: this.activityOwnerName,\r\n                    description: this.activityDescription,\r\n                    date: this.activityDateInput,\r\n                    idea: this.activityIdeaInput,\r\n                    goals: this.activityGoals,\r\n                    audience: this.targetGroups,\r\n                    status: this.activityStatus,\r\n                    governorate: this.activityGovernorate,\r\n                    coordinator: this.coordinatorName\r\n                };\r\n            });\r\n            const activityDeleteButton = document.createElement('button');\r\n            activityDeleteButton.className = 'activity-delete-button';\r\n            activityDeleteButton.textContent = 'حذف النشاط';\r\n            activityDeleteButton.onclick = () => {\r\n                activityDiv.remove();\r\n            };\r\n            activityDiv.appendChild(activityApplyState);\r\n            activityDiv.appendChild(activityDeleteButton);\r\n            this.activities.push(activityDiv);\r\n            document.querySelector('.activities-list').appendChild(activityDiv);\r\n        },\r\n        handleClickOutside(event) {\r\n            const userSection = event.target.closest('.user-section');\r\n            if (!userSection) {\r\n                this.showUserMenu = false;\r\n            }\r\n        },\r\n        setCurrentView(view) {\r\n            this.currentView = view;\r\n            // Automatically fetch activities when switching to view tab\r\n            if (view === 'view') {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        refreshActivities() {\r\n            this.fetchMyActivities();\r\n        },\r\n        async loadCoordinatorName() {\r\n            try {\r\n                const response = await this.$api.get('/coordinator');\r\n\r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n                    this.coordinatorName = data.coordinator_name || '';\r\n                } else if (response.status === 404) {\r\n                    // No coordinator found for user's governorate\r\n                    this.coordinatorName = '';\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error loading coordinator name:', error);\r\n                // Don't show alert for this error as it's not critical\r\n            }\r\n        },\r\n        exportToCSV() {\r\n            if (this.filteredActivities.length === 0) {\r\n                this.$toast.error('لا توجد نشاطات للتصدير');\r\n                return;\r\n            }\r\n\r\n            // Define CSV headers in Arabic\r\n            const headers = [\r\n                'عنوان النشاط',\r\n                'صاحب النشاط', \r\n                'وصف النشاط',\r\n                'تاريخ النشاط',\r\n                'حالة النشاط',\r\n                'المحافظة',\r\n                'منسق المحافظة',\r\n                'تاريخ الإرسال'\r\n            ];\r\n\r\n            // Convert activities data to CSV format\r\n            const csvData = this.filteredActivities.map(activity => {\r\n                return [\r\n                    `\"${activity.title || ''}\"`,\r\n                    `\"${activity.owner_name || ''}\"`,\r\n                    `\"${activity.short_description || ''}\"`,\r\n                    activity.activity_date ? new Date(activity.activity_date).toLocaleDateString('ar-EG') : '',\r\n                    `\"${activity.state || ''}\"`,\r\n                    `\"${activity.submission_info?.governorate || ''}\"`,\r\n                    `\"${activity.submission_info?.coordinator_name || ''}\"`,\r\n                    activity.submission_info?.created_at ? \r\n                    new Date(activity.submission_info.created_at).toLocaleDateString('ar-EG') : ''\r\n                ].join(',');\r\n            });\r\n\r\n            // Combine headers and data\r\n            const csvContent = [headers.join(','), ...csvData].join('\\n');\r\n\r\n            // Add BOM for proper Arabic text encoding in Excel\r\n            const BOM = '\\uFEFF';\r\n            const csvWithBOM = BOM + csvContent;\r\n\r\n            // Create and download the file\r\n            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });\r\n            const link = document.createElement('a');\r\n            \r\n            if (link.download !== undefined) {\r\n                const url = URL.createObjectURL(blob);\r\n                link.setAttribute('href', url);\r\n                \r\n                // Generate filename with current date and filter info\r\n                const currentDate = new Date().toISOString().split('T')[0];\r\n                const filterText = this.selectedFilter ? `_${this.selectedFilter}` : '_جميع_النشاطات';\r\n                const filename = `نشاطات_الفريق_الوطني${filterText}_${currentDate}.csv`;\r\n                \r\n                link.setAttribute('download', filename);\r\n                link.style.visibility = 'hidden';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تصدير ${this.filteredActivities.length} نشاط بنجاح!`);\r\n            } else {\r\n                this.$toast.error('المتصفح لا يدعم تحميل الملفات');\r\n            }\r\n        },\r\n        // PIN Confirmation Methods\r\n        showPinConfirmationModal(action, activity, callback) {\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: action,\r\n                activity: activity,\r\n                callback: callback\r\n            };\r\n            this.showPinConfirmation = true;\r\n            // Focus on PIN input after modal opens\r\n            this.$nextTick(() => {\r\n                const pinInput = document.getElementById('confirmPin');\r\n                if (pinInput) {\r\n                    pinInput.focus();\r\n                }\r\n            });\r\n        },\r\n        closePinConfirmation() {\r\n            this.showPinConfirmation = false;\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: '',\r\n                activity: null,\r\n                callback: null\r\n            };\r\n        },\r\n        async confirmPinAction() {\r\n            const enteredPin = this.pinConfirmationData.pin;\r\n            const storedPin = localStorage.getItem('ndyt_team_pin');\r\n            \r\n            if (enteredPin !== storedPin) {\r\n                this.$toast.error('رمز الفريق الرقمي غير صحيح');\r\n                return;\r\n            }\r\n            \r\n            // Store callback before closing modal\r\n            const callback = this.pinConfirmationData.callback;\r\n            \r\n            // Close modal first\r\n            this.closePinConfirmation();\r\n            \r\n            // Execute the callback function after DOM update\r\n            if (callback) {\r\n                this.$nextTick(async () => {\r\n                    await callback();\r\n                });\r\n            }\r\n        },\r\n        downloadFile(file) {\r\n            try {\r\n                // Create a direct download link\r\n                const fileUrl = `/api/v1/ndyt-activities${file.file_url}`;\r\n                \r\n                // Create a temporary anchor element to trigger download\r\n                const link = document.createElement('a');\r\n                link.href = fileUrl;\r\n                link.download = file.file_name;\r\n                link.target = '_blank'; // Open in new tab as fallback\r\n                link.style.display = 'none';\r\n                \r\n                // Append to body, click, and remove\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تحميل الملف: ${file.file_name}`);\r\n            } catch (error) {\r\n                console.error('Download error:', error);\r\n                this.$toast.error(`فشل في تحميل الملف: ${file.file_name}`);\r\n            }\r\n        },\r\n        getShortFileName(fileName) {\r\n            // Truncate long file names for display\r\n            if (fileName.length > 20) {\r\n                const extension = fileName.split('.').pop();\r\n                const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));\r\n                return nameWithoutExt.substring(0, 15) + '...' + '.' + extension;\r\n            }\r\n            return fileName;\r\n        },\r\n        openActivityModal(activity) {\r\n            this.selectedActivity = activity;\r\n            this.showActivityModal = true;\r\n            // Prevent body scrolling when modal is open\r\n            document.body.style.overflow = 'hidden';\r\n        },\r\n        closeActivityModal() {\r\n            this.showActivityModal = false;\r\n            this.selectedActivity = null;\r\n            // Restore body scrolling when modal is closed\r\n            document.body.style.overflow = 'auto';\r\n        },\r\n        formatDate(dateString) {\r\n            if (!dateString) return 'غير محدد';\r\n            const date = new Date(dateString);\r\n            return date.toLocaleDateString('ar-EG', {\r\n                year: 'numeric',\r\n                month: 'long',\r\n                day: 'numeric'\r\n            });\r\n        },\r\n        formatTime(timeString) {\r\n            if (!timeString) return 'غير محدد';\r\n            return timeString;\r\n        },\r\n        formatCurrency(amount) {\r\n            if (!amount) return 'غير محدد';\r\n            return new Intl.NumberFormat('ar-EG').format(amount) + ' د.ع';\r\n        },\r\n        async loadImageAsArrayBuffer(imagePath) {\r\n            try {\r\n                // Import the image as a module to get the correct URL\r\n                let imageUrl;\r\n                if (imagePath.includes('scy_logo')) {\r\n                    imageUrl = (await import('@/assets/scy_logo.jpg')).default;\r\n                } else if (imagePath.includes('ndyt_logo')) {\r\n                    imageUrl = (await import('@/assets/ndyt_logo.jpg')).default;\r\n                }\r\n\r\n                if (!imageUrl) throw new Error(`Could not resolve image: ${imagePath}`);\r\n\r\n                const response = await fetch(imageUrl);\r\n                if (!response.ok) throw new Error(`Failed to load image: ${imageUrl}`);\r\n                return await response.arrayBuffer();\r\n            } catch (error) {\r\n                console.error('Error loading image:', error);\r\n                return null;\r\n            }\r\n        },\r\n        async exportToDocx() {\r\n            if (!this.selectedActivity) return;\r\n\r\n            const activity = this.selectedActivity;\r\n\r\n            // Load logo images\r\n            const scyLogoBuffer = await this.loadImageAsArrayBuffer('scy_logo');\r\n            const ndytLogoBuffer = await this.loadImageAsArrayBuffer('ndyt_logo');\r\n\r\n            // Create document sections\r\n            const sections = [];\r\n\r\n            // Document header with logos and organization names\r\n            const headerParagraphs = [];\r\n\r\n            // Header with logos and organization names in one line\r\n            if (scyLogoBuffer && ndytLogoBuffer) {\r\n                // Create a table for proper logo and text alignment\r\n                const headerTable = new Table({\r\n                    width: {\r\n                        size: 100,\r\n                        type: WidthType.PERCENTAGE,\r\n                    },\r\n                    borders: {\r\n                        top: { style: BorderStyle.NONE },\r\n                        bottom: { style: BorderStyle.NONE },\r\n                        left: { style: BorderStyle.NONE },\r\n                        right: { style: BorderStyle.NONE },\r\n                        insideHorizontal: { style: BorderStyle.NONE },\r\n                        insideVertical: { style: BorderStyle.NONE },\r\n                    },\r\n                    rows: [\r\n                        new TableRow({\r\n                            children: [\r\n                                // SCY Logo\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [\r\n                                            new ImageRun({\r\n                                                data: scyLogoBuffer,\r\n                                                transformation: {\r\n                                                    width: 80,\r\n                                                    height: 80,\r\n                                                },\r\n                                            }),\r\n                                        ],\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                }),\r\n                                // Organization Names\r\n                                new TableCell({\r\n                                    children: [\r\n                                        new Paragraph({\r\n                                            children: [\r\n                                                new TextRun({\r\n                                                    text: \"المجلس الأعلى للشباب\",\r\n                                                    bold: true,\r\n                                                    size: 32,\r\n                                                    font: \"Arial\",\r\n                                                }),\r\n                                            ],\r\n                                            alignment: AlignmentType.CENTER,\r\n                                            spacing: { after: 200 },\r\n                                            bidirectional: true,\r\n                                        }),\r\n                                        new Paragraph({\r\n                                            children: [\r\n                                                new TextRun({\r\n                                                    text: \"الفريق الوطني للشباب الرقمي\",\r\n                                                    bold: true,\r\n                                                    size: 28,\r\n                                                    font: \"Arial\",\r\n                                                }),\r\n                                            ],\r\n                                            alignment: AlignmentType.CENTER,\r\n                                            spacing: { after: 200 },\r\n                                            bidirectional: true,\r\n                                        }),\r\n                                    ],\r\n                                    width: { size: 60, type: WidthType.PERCENTAGE },\r\n                                }),\r\n                                // NDYT Logo\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [\r\n                                            new ImageRun({\r\n                                                data: ndytLogoBuffer,\r\n                                                transformation: {\r\n                                                    width: 80,\r\n                                                    height: 80,\r\n                                                },\r\n                                            }),\r\n                                        ],\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                }),\r\n                            ],\r\n                        }),\r\n                    ],\r\n                });\r\n\r\n                headerParagraphs.push(headerTable);\r\n            } else {\r\n                // Fallback if images don't load\r\n                headerParagraphs.push(\r\n                    new Paragraph({\r\n                        children: [\r\n                            new TextRun({\r\n                                text: \"المجلس الأعلى للشباب\",\r\n                                bold: true,\r\n                                size: 32,\r\n                                font: \"Arial\",\r\n                            }),\r\n                        ],\r\n                        alignment: AlignmentType.CENTER,\r\n                        spacing: { after: 200 },\r\n                        bidirectional: true,\r\n                    }),\r\n                    new Paragraph({\r\n                        children: [\r\n                            new TextRun({\r\n                                text: \"الفريق الوطني للشباب الرقمي\",\r\n                                bold: true,\r\n                                size: 28,\r\n                                font: \"Arial\",\r\n                            }),\r\n                        ],\r\n                        alignment: AlignmentType.CENTER,\r\n                        spacing: { after: 400 },\r\n                        bidirectional: true,\r\n                    })\r\n                );\r\n            }\r\n\r\n            // Activity title\r\n            headerParagraphs.push(\r\n                new Paragraph({\r\n                    children: [\r\n                        new TextRun({\r\n                            text: activity.title,\r\n                            bold: true,\r\n                            size: 36,\r\n                            underline: {},\r\n                            font: \"Arial\",\r\n                        }),\r\n                    ],\r\n                    alignment: AlignmentType.CENTER,\r\n                    spacing: { before: 400, after: 600 },\r\n                    bidirectional: true,\r\n                })\r\n            );\r\n\r\n            // Activity details table\r\n            const detailsTable = new Table({\r\n                width: {\r\n                    size: 100,\r\n                    type: WidthType.PERCENTAGE,\r\n                },\r\n                borders: {\r\n                    top: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    bottom: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    left: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    right: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    insideHorizontal: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                    insideVertical: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                },\r\n                rows: [\r\n                    // Basic information rows\r\n                    this.createTableRow(\"الاسم\", `الفريق الوطني للشباب الرقمي - ${activity.governorate} - ${activity.owner_name}`),\r\n                    this.createTableRow(\"اسم النشاط\", activity.title),\r\n                    ...(activity.activity_idea ? [this.createTableRow(\"فكرة النشاط\", activity.activity_idea)] : []),\r\n                    ...(activity.activity_goals && activity.activity_goals.length > 0 ?\r\n                        [this.createTableRow(\"أهداف النشاط\", activity.activity_goals.map(g => `• ${g.text}`).join('\\n'))] : []),\r\n                    ...(activity.target_groups && activity.target_groups.length > 0 ?\r\n                        [this.createTableRow(\"الفئة المستهدفة\", activity.target_groups.map(g => g.text).join(', '))] : []),\r\n                    ...(activity.audience_count ? [this.createTableRow(\"عدد المستهدفين\", `${activity.audience_count} شخص`)] : []),\r\n                    this.createTableRow(\"تاريخ النشاط\", this.formatDate(activity.activity_date)),\r\n                    ...(activity.activity_time ? [this.createTableRow(\"وقت النشاط\", this.formatTime(activity.activity_time))] : []),\r\n                    ...(activity.activity_location ? [this.createTableRow(\"مكان النشاط\", activity.activity_location)] : []),\r\n                    ...(activity.activity_duration ? [this.createTableRow(\"مدة النشاط\", activity.activity_duration)] : []),\r\n                    ...(activity.activity_budget ? [this.createTableRow(\"ميزانية النشاط\", this.formatCurrency(activity.activity_budget))] : []),\r\n                    ...(activity.activity_levels && activity.activity_levels.length > 0 ?\r\n                        [this.createTableRow(\"مراحل تنفيذ النشاط\", activity.activity_levels.map(l => l.description).join('\\n'))] : []),\r\n                    this.createTableRow(\"حالة النشاط\", activity.state),\r\n                    this.createTableRow(\"المحافظة\", activity.governorate),\r\n                    this.createTableRow(\"منسق المحافظة\", activity.coordinator_name),\r\n                    this.createTableRow(\"تاريخ الإرسال\", this.formatDate(activity.created_at)),\r\n                ],\r\n            });\r\n\r\n            sections.push(...headerParagraphs, detailsTable);\r\n\r\n            // Budget details table if exists\r\n            if (activity.budget_details && activity.budget_details.length > 0) {\r\n                sections.push(\r\n                    new Paragraph({\r\n                        children: [\r\n                            new TextRun({\r\n                                text: \"تفاصيل الصرف\",\r\n                                bold: true,\r\n                                size: 28,\r\n                                font: \"Arial\",\r\n                            }),\r\n                        ],\r\n                        spacing: { before: 400, after: 200 },\r\n                        bidirectional: true,\r\n                        alignment: AlignmentType.CENTER,\r\n                    })\r\n                );\r\n\r\n                const budgetTable = new Table({\r\n                    width: {\r\n                        size: 100,\r\n                        type: WidthType.PERCENTAGE,\r\n                    },\r\n                    borders: {\r\n                        top: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        bottom: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        left: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        right: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        insideHorizontal: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                        insideVertical: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                    },\r\n                    rows: [\r\n                        // Header row\r\n                        new TableRow({\r\n                            children: [\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"المادة\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: {\r\n                                        fill: \"4472C4\", // Blue header background\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: {\r\n                                        top: 100,\r\n                                        bottom: 100,\r\n                                        left: 100,\r\n                                        right: 100,\r\n                                    },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"النوع\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"العدد\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"السعر\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"المجموع\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                            ],\r\n                        }),\r\n                        // Data rows\r\n                        ...activity.budget_details.map((detail, index) => new TableRow({\r\n                            children: [\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: detail.name || '',\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\", // Alternating row colors\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: detail.type || '',\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: detail.amount?.toString() || '0',\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: this.formatCurrency(detail.price),\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: this.formatCurrency(detail.budgetPrice),\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                            ],\r\n                        })),\r\n                        // Total row if total budget exists\r\n                        ...(activity.total_budget ? [new TableRow({\r\n                            children: [\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"الإجمالي\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    columnSpan: 4,\r\n                                    shading: {\r\n                                        fill: \"2F5597\", // Dark blue for total row\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: this.formatCurrency(activity.total_budget),\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: \"2F5597\", // Dark blue for total row\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                            ],\r\n                        })] : []),\r\n                    ],\r\n                });\r\n\r\n                sections.push(budgetTable);\r\n            }\r\n\r\n            // Create document with RTL support\r\n            const doc = new Document({\r\n                sections: [{\r\n                    children: sections,\r\n                    properties: {\r\n                        page: {\r\n                            margin: {\r\n                                top: 1440,    // 1 inch\r\n                                right: 1440,  // 1 inch\r\n                                bottom: 1440, // 1 inch\r\n                                left: 1440,   // 1 inch\r\n                            },\r\n                        },\r\n                    },\r\n                }],\r\n                styles: {\r\n                    default: {\r\n                        document: {\r\n                            run: {\r\n                                font: \"Arial\",\r\n                                size: 24,\r\n                            },\r\n                            paragraph: {\r\n                                spacing: {\r\n                                    line: 276,\r\n                                },\r\n                            },\r\n                        },\r\n                    },\r\n                },\r\n            });\r\n\r\n            // Generate and save file\r\n            try {\r\n                const blob = await Packer.toBlob(doc);\r\n                const fileName = `نشاط_${activity.title.replace(/[^\\w\\s]/gi, '')}_${new Date().toISOString().split('T')[0]}.docx`;\r\n                saveAs(blob, fileName);\r\n\r\n                this.$toast.success('تم تصدير الملف بنجاح');\r\n            } catch (error) {\r\n                console.error('Error exporting DOCX:', error);\r\n                this.$toast.error('حدث خطأ أثناء تصدير الملف');\r\n            }\r\n        },\r\n        createTableRow(label, value) {\r\n            return new TableRow({\r\n                children: [\r\n                    // Value cell first (right side in RTL)\r\n                    new TableCell({\r\n                        children: [new Paragraph({\r\n                            children: [new TextRun({\r\n                                text: value || 'غير محدد',\r\n                                font: \"Arial\",\r\n                                size: 24,\r\n                                color: \"000000\",\r\n                            })],\r\n                            bidirectional: true,\r\n                            alignment: AlignmentType.RIGHT,\r\n                        })],\r\n                        width: { size: 70, type: WidthType.PERCENTAGE },\r\n                        shading: {\r\n                            fill: \"FFFFFF\", // White background for values\r\n                        },\r\n                        borders: {\r\n                            top: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            bottom: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            left: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            right: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                        },\r\n                        margins: {\r\n                            top: 100,\r\n                            bottom: 100,\r\n                            left: 150,\r\n                            right: 150,\r\n                        },\r\n                    }),\r\n                    // Label cell second (left side in RTL)\r\n                    new TableCell({\r\n                        children: [new Paragraph({\r\n                            children: [new TextRun({\r\n                                text: label,\r\n                                bold: true,\r\n                                font: \"Arial\",\r\n                                size: 24,\r\n                                color: \"000000\",\r\n                            })],\r\n                            bidirectional: true,\r\n                            alignment: AlignmentType.RIGHT,\r\n                        })],\r\n                        width: { size: 30, type: WidthType.PERCENTAGE },\r\n                        shading: {\r\n                            fill: \"E6E6FA\", // Light lavender background for labels\r\n                        },\r\n                        borders: {\r\n                            top: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            bottom: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            left: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            right: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                        },\r\n                        margins: {\r\n                            top: 100,\r\n                            bottom: 100,\r\n                            left: 150,\r\n                            right: 150,\r\n                        },\r\n                    }),\r\n                ],\r\n            });\r\n        },\r\n        async printActivity() {\r\n            if (!this.selectedActivity) return;\r\n\r\n            try {\r\n                // Get the image URLs from the current page\r\n                const scyLogoImg = document.querySelector('img[src*=\"scy_logo\"]');\r\n                const ndytLogoImg = document.querySelector('img[src*=\"ndyt_logo\"]');\r\n\r\n                const scyLogoSrc = scyLogoImg ? scyLogoImg.src : '';\r\n                const ndytLogoSrc = ndytLogoImg ? ndytLogoImg.src : '';\r\n\r\n                // Create a new window for printing\r\n                const printWindow = window.open('', '_blank');\r\n\r\n                // Generate the print content with correct image URLs\r\n                const printContent = this.generatePrintContent(this.selectedActivity, scyLogoSrc, ndytLogoSrc);\r\n\r\n                // Write the content to the new window\r\n                printWindow.document.write(printContent);\r\n                printWindow.document.close();\r\n\r\n                // Wait for images to load, then print\r\n                printWindow.onload = () => {\r\n                    // Wait a bit longer for images to fully load\r\n                    setTimeout(() => {\r\n                        printWindow.print();\r\n                        printWindow.close();\r\n                    }, 1000);\r\n                };\r\n            } catch (error) {\r\n                console.error('Error printing activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء الطباعة');\r\n            }\r\n        },\r\n        generatePrintContent(activity, scyLogoSrc = '', ndytLogoSrc = '') {\r\n            return `\r\n<!DOCTYPE html>\r\n<html dir=\"rtl\" lang=\"ar\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>تفاصيل النشاط - ${activity.title}</title>\r\n    <style>\r\n        @page {\r\n            size: A4;\r\n            margin: 2cm;\r\n        }\r\n\r\n        * {\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        body {\r\n            font-family: 'Arial', sans-serif;\r\n            line-height: 1.6;\r\n            color: #000;\r\n            background: white;\r\n            direction: rtl;\r\n        }\r\n\r\n        .document-header {\r\n            text-align: center;\r\n            margin-bottom: 30px;\r\n            border-bottom: 3px solid #000;\r\n            padding-bottom: 20px;\r\n        }\r\n\r\n        .logo-section {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n        }\r\n\r\n        .logo-container {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            width: 120px;\r\n        }\r\n\r\n        .organization-logo {\r\n            width: 80px;\r\n            height: 80px;\r\n            border-radius: 50%;\r\n            border: 2px solid #333;\r\n            object-fit: cover;\r\n        }\r\n\r\n        .logo-label {\r\n            font-size: 12px;\r\n            margin-top: 5px;\r\n            color: #666;\r\n        }\r\n\r\n        .organization-info {\r\n            flex: 1;\r\n            text-align: center;\r\n        }\r\n\r\n        .organization-info h2 {\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            margin-bottom: 10px;\r\n            color: #000;\r\n        }\r\n\r\n        .organization-info h3 {\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n            color: #333;\r\n        }\r\n\r\n        .document-title h1 {\r\n            font-size: 32px;\r\n            font-weight: bold;\r\n            text-decoration: underline;\r\n            margin-top: 20px;\r\n            color: #000;\r\n        }\r\n\r\n        .details-table {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin-top: 30px;\r\n        }\r\n\r\n        .detail-row {\r\n            display: table-row;\r\n        }\r\n\r\n        .detail-label {\r\n            display: table-cell;\r\n            background-color: #f0f0f0;\r\n            border: 2px solid #333;\r\n            padding: 12px 15px;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            width: 30%;\r\n            vertical-align: top;\r\n            text-align: right;\r\n        }\r\n\r\n        .detail-value {\r\n            display: table-cell;\r\n            border: 2px solid #333;\r\n            padding: 12px 15px;\r\n            font-size: 16px;\r\n            width: 70%;\r\n            vertical-align: top;\r\n            text-align: right;\r\n        }\r\n\r\n        .goals-list, .target-groups-list, .levels-list {\r\n            list-style: none;\r\n            padding: 0;\r\n        }\r\n\r\n        .goals-list li, .levels-list li {\r\n            margin-bottom: 5px;\r\n            padding-right: 20px;\r\n            position: relative;\r\n        }\r\n\r\n        .goals-list li:before {\r\n            content: \"•\";\r\n            position: absolute;\r\n            right: 0;\r\n            font-weight: bold;\r\n        }\r\n\r\n        .target-groups-list li {\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n        }\r\n\r\n        .target-groups-list li:not(:last-child):after {\r\n            content: \"،\";\r\n        }\r\n\r\n        .status-badge {\r\n            padding: 6px 12px;\r\n            border-radius: 20px;\r\n            font-weight: bold;\r\n            font-size: 14px;\r\n        }\r\n\r\n        .status-executed-paid { background-color: #d4edda; color: #155724; }\r\n        .status-executed-unpaid { background-color: #fff3cd; color: #856404; }\r\n        .status-accepted { background-color: #cce5ff; color: #004085; }\r\n        .status-rejected { background-color: #f8d7da; color: #721c24; }\r\n        .status-needs-edit { background-color: #ffeaa7; color: #6c5ce7; }\r\n        .status-paid-not-executed { background-color: #fab1a0; color: #e17055; }\r\n        .status-accepted-unpaid { background-color: #a8e6cf; color: #00b894; }\r\n        .status-sent { background-color: #e0e0e0; color: #636e72; }\r\n\r\n        .budget-table {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin-top: 10px;\r\n        }\r\n\r\n        .budget-table th {\r\n            background-color: #4472C4;\r\n            color: white;\r\n            border: 2px solid #000;\r\n            padding: 10px;\r\n            text-align: center;\r\n            font-weight: bold;\r\n        }\r\n\r\n        .budget-table td {\r\n            border: 1px solid #666;\r\n            padding: 8px;\r\n            text-align: center;\r\n        }\r\n\r\n        .budget-table tbody tr:nth-child(even) {\r\n            background-color: #f2f2f2;\r\n        }\r\n\r\n        .budget-table .total-row {\r\n            background-color: #2F5597 !important;\r\n            color: white;\r\n            font-weight: bold;\r\n        }\r\n\r\n        .budget-table .total-row td {\r\n            border: 3px solid #000;\r\n        }\r\n\r\n        @media print {\r\n            body {\r\n                -webkit-print-color-adjust: exact;\r\n                print-color-adjust: exact;\r\n            }\r\n\r\n            .no-print {\r\n                display: none !important;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"document-header\">\r\n        <div class=\"logo-section\">\r\n            <div class=\"logo-container\">\r\n                ${scyLogoSrc ? `<img src=\"${scyLogoSrc}\" alt=\"شعار المجلس الأعلى للشباب\" class=\"organization-logo\" />` : ''}\r\n                <span class=\"logo-label\">المجلس الأعلى للشباب</span>\r\n            </div>\r\n            <div class=\"organization-info\">\r\n                <h2>المجلس الأعلى للشباب</h2>\r\n                <h3>الفريق الوطني للشباب الرقمي</h3>\r\n            </div>\r\n            <div class=\"logo-container\">\r\n                ${ndytLogoSrc ? `<img src=\"${ndytLogoSrc}\" alt=\"شعار الفريق الوطني للشباب الرقمي\" class=\"organization-logo\" />` : ''}\r\n                <span class=\"logo-label\">الفريق الوطني للشباب الرقمي</span>\r\n            </div>\r\n        </div>\r\n        <div class=\"document-title\">\r\n            <h1>${activity.title}</h1>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"details-table\">\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">الاسم</div>\r\n            <div class=\"detail-value\">الفريق الوطني للشباب الرقمي - ${activity.governorate} - ${activity.owner_name}</div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">اسم النشاط</div>\r\n            <div class=\"detail-value\">${activity.title}</div>\r\n        </div>\r\n\r\n        ${activity.activity_idea ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">فكرة النشاط</div>\r\n            <div class=\"detail-value\">${activity.activity_idea}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_goals && activity.activity_goals.length > 0 ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">أهداف النشاط</div>\r\n            <div class=\"detail-value\">\r\n                <ul class=\"goals-list\">\r\n                    ${activity.activity_goals.map(goal => `<li>${goal.text}</li>`).join('')}\r\n                </ul>\r\n            </div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.target_groups && activity.target_groups.length > 0 ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">الفئة المستهدفة</div>\r\n            <div class=\"detail-value\">\r\n                <ul class=\"target-groups-list\">\r\n                    ${activity.target_groups.map(group => `<li>${group.text}</li>`).join('')}\r\n                </ul>\r\n            </div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.audience_count ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">عدد المستهدفين</div>\r\n            <div class=\"detail-value\">${activity.audience_count} شخص</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">تاريخ النشاط</div>\r\n            <div class=\"detail-value\">${this.formatDate(activity.activity_date)}</div>\r\n        </div>\r\n\r\n        ${activity.activity_time ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">وقت النشاط</div>\r\n            <div class=\"detail-value\">${this.formatTime(activity.activity_time)}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_location ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">مكان النشاط</div>\r\n            <div class=\"detail-value\">${activity.activity_location}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_duration ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">مدة النشاط</div>\r\n            <div class=\"detail-value\">${activity.activity_duration}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_budget ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">ميزانية النشاط</div>\r\n            <div class=\"detail-value\">${this.formatCurrency(activity.activity_budget)}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_levels && activity.activity_levels.length > 0 ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">مراحل تنفيذ النشاط</div>\r\n            <div class=\"detail-value\">\r\n                <ul class=\"levels-list\">\r\n                    ${activity.activity_levels.map(level => `<li>${level.description}</li>`).join('')}\r\n                </ul>\r\n            </div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">حالة النشاط</div>\r\n            <div class=\"detail-value\">\r\n                <span class=\"status-badge ${this.getStatusClass(activity.state)}\">\r\n                    ${activity.state}\r\n                </span>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">المحافظة</div>\r\n            <div class=\"detail-value\">${activity.governorate}</div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">منسق المحافظة</div>\r\n            <div class=\"detail-value\">${activity.coordinator_name}</div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">تاريخ الإرسال</div>\r\n            <div class=\"detail-value\">${this.formatDate(activity.created_at)}</div>\r\n        </div>\r\n    </div>\r\n\r\n    ${activity.budget_details && activity.budget_details.length > 0 ? `\r\n    <div style=\"margin-top: 30px;\">\r\n        <h3 style=\"text-align: center; margin-bottom: 20px; font-size: 24px;\">تفاصيل الصرف</h3>\r\n        <table class=\"budget-table\">\r\n            <thead>\r\n                <tr>\r\n                    <th>المادة</th>\r\n                    <th>النوع</th>\r\n                    <th>العدد</th>\r\n                    <th>السعر</th>\r\n                    <th>المجموع</th>\r\n                </tr>\r\n            </thead>\r\n            <tbody>\r\n                ${activity.budget_details.map((detail, index) => `\r\n                <tr ${index % 2 === 0 ? 'style=\"background-color: #f2f2f2;\"' : ''}>\r\n                    <td>${detail.name || ''}</td>\r\n                    <td>${detail.type || ''}</td>\r\n                    <td>${detail.amount || ''}</td>\r\n                    <td>${this.formatCurrency(detail.price)}</td>\r\n                    <td>${this.formatCurrency(detail.budgetPrice)}</td>\r\n                </tr>\r\n                `).join('')}\r\n            </tbody>\r\n            ${activity.total_budget ? `\r\n            <tfoot>\r\n                <tr class=\"total-row\">\r\n                    <td colspan=\"4\"><strong>الإجمالي</strong></td>\r\n                    <td><strong>${this.formatCurrency(activity.total_budget)}</strong></td>\r\n                </tr>\r\n            </tfoot>\r\n            ` : ''}\r\n        </table>\r\n    </div>\r\n    ` : ''}\r\n</body>\r\n</html>\r\n            `;\r\n        }\r\n    },\r\n    mounted() {\r\n        // Get user info from localStorage\r\n        const userStr = localStorage.getItem('ndyt_user');\r\n        if (userStr) {\r\n            this.user = JSON.parse(userStr);\r\n        }\r\n        // Add click outside listener\r\n        document.addEventListener('click', this.handleClickOutside);\r\n        // Load coordinator name from previous submissions\r\n        this.loadCoordinatorName();\r\n    },\r\n    beforeUnmount() {\r\n        document.removeEventListener('click', this.handleClickOutside);\r\n        // Ensure body scroll is restored if component is destroyed while modal is open\r\n        document.body.style.overflow = 'auto';\r\n    }\r\n}\r\n</script>\r\n<template>\r\n    <div class=\"navbar\">\r\n        <div class=\"navbar-content\">\r\n            <div class=\"navbar-text\">\r\n                <span class=\"org-name\">المجلس الأعلى للشباب</span>\r\n                <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n                <span class=\"team-name\">الفريق الوطني للشباب الرقمي</span>\r\n            </div>\r\n            <div class=\"nav-actions\">\r\n                <button class=\"nav-btn\" :class=\"{ active: currentView === 'submit' }\" @click=\"setCurrentView('submit')\">\r\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                        <path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/>\r\n                    </svg>\r\n                    <span>إرسال النشاطات</span>\r\n                </button>\r\n                <button class=\"nav-btn\" :class=\"{ active: currentView === 'view' }\" @click=\"setCurrentView('view')\">\r\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                        <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\r\n                    </svg>\r\n                    <span>النشاطات المرسلة</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"user-section\">\r\n                <div class=\"user-button\" @click=\"toggleUserMenu\">\r\n                    <div class=\"user-avatar\">\r\n                        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\r\n                        </svg>\r\n                    </div>\r\n                    <div class=\"user-info\">\r\n                        <span class=\"user-name\">{{ user?.full_name || user?.username || 'المستخدم' }}</span>\r\n                        <svg class=\"dropdown-arrow\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M7 10l5 5 5-5z\"/>\r\n                        </svg>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"showUserMenu\" class=\"user-menu\">\r\n                    <div class=\"user-menu-item\" @click=\"openAccountSettings\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"/>\r\n                        </svg>\r\n                        <span>إعدادات الحساب</span>\r\n                    </div>\r\n                    <div v-if=\"user && user.rank === 'admin'\" class=\"user-menu-item\" @click=\"goToAdmin\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17.5L18 14.5V9H6V14.5L12 17.5Z\"/>\r\n                        </svg>\r\n                        <span>لوحة الإدارة</span>\r\n                    </div>\r\n                    <div class=\"user-menu-item\" @click=\"logout\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"/>\r\n                        </svg>\r\n                        <span>تسجيل الخروج</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"main-content\">\r\n        <!-- Submit Activities View -->\r\n        <div v-if=\"currentView === 'submit'\" class=\"submit-view\">\r\n            <div class=\"view-header\">\r\n                <h2 class=\"view-title\">إرسال النشاطات الجديدة</h2>\r\n                <p class=\"view-description\">قم بإدخال المعلومات الأساسية ونشاطاتك لإرسالها للمراجعة</p>\r\n            </div>\r\n            <div class=\"base-info-container\">\r\n                <span class=\"base-info-label\">المعلومات الأساسية</span>\r\n                <label for=\"coordinator-name\" class=\"field-label\">منسق المحافظة</label>\r\n                <input type=\"text\" id=\"coordinator-name\" placeholder=\"اسم منسق المحافظة\" class=\"base-info-input\" v-model=\"coordinatorName\" disabled required>\r\n            </div>\r\n            \r\n            <div class=\"splitter\"></div>\r\n            \r\n            <div class=\"activities-info-container\">\r\n                <span class=\"base-info-label\">إضافة نشاطات جديدة</span>\r\n                <div class=\"activities-list\">\r\n                </div>\r\n                <button style=\"margin: 10px; max-width: 250px;\" @click=\"AddActivityItem\">\r\n                    <span>إضافة نشاط جديد</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"splitter\"></div>\r\n            <button style=\"margin: 0 50px; background-color: orange; max-width: 180px;\" @click=\"submitCV\">\r\n                <span>إرسال النشاطات</span>\r\n            </button>\r\n        </div>\r\n        \r\n        <!-- View Activities Section -->\r\n        <div v-if=\"currentView === 'view'\" class=\"view-activities\">\r\n            <div class=\"view-header\">\r\n                <div class=\"view-header-content\">\r\n                    <h2 class=\"view-title\">النشاطات</h2>\r\n                    <p class=\"view-description\">عرض وإدارة النشاطات حسب صلاحياتك (المدير: جميع النشاطات، المنسق: نشاطات المحافظة، العضو: نشاطاتك الشخصية)</p>\r\n                </div>\r\n                <div class=\"header-actions\">\r\n                    <button @click=\"exportToCSV\" class=\"export-btn\" :disabled=\"loadingActivities || filteredActivities.length === 0\" title=\"تصدير إلى Excel\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                        </svg>\r\n                        <span>تصدير CSV</span>\r\n                    </button>\r\n                    <button @click=\"refreshActivities\" class=\"refresh-btn\" :disabled=\"loadingActivities\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\" :class=\"{ 'spinning': loadingActivities }\">\r\n                            <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\r\n                        </svg>\r\n                        <span>تحديث</span>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"activities-container\">\r\n                <div v-if=\"loadingActivities\" class=\"loading-message\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <span>جاري تحميل النشاطات...</span>\r\n                </div>\r\n                \r\n                <div v-else-if=\"myActivities.length === 0\" class=\"no-activities-message\">\r\n                    <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\" class=\"empty-icon\">\r\n                        <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n                    </svg>\r\n                    <h3>لا توجد نشاطات</h3>\r\n                    <p>لا توجد نشاطات متاحة حسب صلاحياتك حالياً</p>\r\n                </div>\r\n                \r\n                <!-- Edit Form Modal -->\r\n                <div v-if=\"editingActivity\" class=\"edit-modal-overlay\" @click=\"cancelEdit\">\r\n                    <div class=\"edit-modal\" @click.stop>\r\n                        <div class=\"edit-modal-header\">\r\n                            <h3>تعديل النشاط</h3>\r\n                            <button @click=\"cancelEdit\" class=\"close-btn\">\r\n                                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                    <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                </svg>\r\n                            </button>\r\n                        </div>\r\n                        <div class=\"edit-form\">\r\n                            <div class=\"form-row\">\r\n                                <div class=\"form-group\">\r\n                                    <label>اسم صاحب النشاط:</label>\r\n                                    <input v-model=\"editingActivity.owner_name\" type=\"text\" class=\"form-input\">\r\n                                </div>\r\n                                <div class=\"form-group\">\r\n                                    <label>عنوان النشاط:</label>\r\n                                    <input v-model=\"editingActivity.title\" type=\"text\" class=\"form-input\">\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"form-row\">\r\n                                <div class=\"form-group\">\r\n                                    <label>تاريخ النشاط:</label>\r\n                                    <input v-model=\"editingActivity.activity_date\" type=\"date\" class=\"form-input\">\r\n                                </div>\r\n                                <div class=\"form-group\">\r\n                                    <label>حالة النشاط:</label>\r\n                                    <select v-model=\"editingActivity.state\" class=\"form-select\">\r\n                                        <option value=\"مرسل\">مرسل</option>\r\n                                        <option value=\"منفذ بصرف\">منفذ بصرف</option>\r\n                                        <option value=\"منفذ بدون صرف\">منفذ بدون صرف</option>\r\n                                        <option value=\"مقبول\">مقبول</option>\r\n                                        <option value=\"مرفوض\">مرفوض</option>\r\n                                        <option value=\"يحتاج تعديل\">يحتاج تعديل</option>\r\n                                        <option value=\"صرف و لم ينفذ\">صرف و لم ينفذ</option>\r\n                                        <option value=\"مقبول دون صرف\">مقبول دون صرف</option>\r\n                                    </select>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"form-row\">\r\n                                <div class=\"form-group full-width\">\r\n                                    <label>وصف مختصر:</label>\r\n                                    <textarea v-model=\"editingActivity.short_description\" class=\"form-textarea\" rows=\"3\"></textarea>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"form-actions\">\r\n                                <button @click=\"saveActivity\" class=\"save-btn\">\r\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                        <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"/>\r\n                                    </svg>\r\n                                    حفظ التغييرات\r\n                                </button>\r\n                                <button @click=\"cancelEdit\" class=\"cancel-btn\">\r\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                        <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                    </svg>\r\n                                    إلغاء\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                \r\n                <!-- Activities Table -->\r\n                <div v-else-if=\"myActivities.length > 0\" class=\"activities-table-container\">\r\n                    <div class=\"table-header\">\r\n                        <!-- Search Box -->\r\n                        <div class=\"search-container\">\r\n                            <div class=\"search-box\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    v-model=\"searchQuery\"\r\n                                    placeholder=\"البحث بعنوان النشاط أو اسم صاحب النشاط...\"\r\n                                    class=\"search-input\"\r\n                                />\r\n                                <svg\r\n                                    class=\"search-icon\"\r\n                                    width=\"20\"\r\n                                    height=\"20\"\r\n                                    viewBox=\"0 0 24 24\"\r\n                                    fill=\"currentColor\"\r\n                                    @click=\"handleSearchIconClick\"\r\n                                    :title=\"searchQuery ? 'مسح البحث' : 'بحث'\"\r\n                                    :aria-label=\"searchQuery ? 'مسح البحث' : 'بحث'\"\r\n                                >\r\n                                    <path v-if=\"!searchQuery\" d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\r\n                                    <path v-else d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                </svg>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"table-stats\">\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === null }\" @click=\"selectFilter(null)\">\r\n                                <span class=\"stat-number\">{{ myActivities.length }}</span>\r\n                                <span class=\"stat-label\">إجمالي النشاطات</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بصرف' }\" @click=\"selectFilter('منفذ بصرف')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بصرف').length }}</span>\r\n                                <span class=\"stat-label\">منفذ بصرف</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بدون صرف' }\" @click=\"selectFilter('منفذ بدون صرف')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بدون صرف').length }}</span>\r\n                                <span class=\"stat-label\">منفذ بدون صرف</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول' }\" @click=\"selectFilter('مقبول')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول').length }}</span>\r\n                                <span class=\"stat-label\">مقبول</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرفوض' }\" @click=\"selectFilter('مرفوض')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرفوض').length }}</span>\r\n                                <span class=\"stat-label\">مرفوض</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'يحتاج تعديل' }\" @click=\"selectFilter('يحتاج تعديل')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'يحتاج تعديل').length }}</span>\r\n                                <span class=\"stat-label\">يحتاج تعديل</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'صرف و لم ينفذ' }\" @click=\"selectFilter('صرف و لم ينفذ')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'صرف و لم ينفذ').length }}</span>\r\n                                <span class=\"stat-label\">صرف و لم ينفذ</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول دون صرف' }\" @click=\"selectFilter('مقبول دون صرف')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول دون صرف').length }}</span>\r\n                                <span class=\"stat-label\">مقبول دون صرف</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرسل' }\" @click=\"selectFilter('مرسل')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرسل').length }}</span>\r\n                                <span class=\"stat-label\">مرسل</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"table-wrapper\">\r\n                        <table class=\"activities-table\">\r\n                            <thead>\r\n                                <tr>\r\n                                    <th class=\"col-title\">عنوان النشاط</th>\r\n                                    <th class=\"col-owner\">صاحب النشاط</th>\r\n                                    <th class=\"col-date\">تاريخ النشاط</th>\r\n                                    <th class=\"col-status\">الحالة</th>\r\n                                    <th class=\"col-governorate\">المحافظة</th>\r\n                                    <th class=\"col-coordinator\">المنسق</th>\r\n                                    <th class=\"col-actions\">الإجراءات</th>\r\n                                </tr>\r\n                            </thead>\r\n                            <tbody>\r\n                                <tr v-for=\"activity in filteredActivities\" :key=\"activity.id\" class=\"activity-row clickable-row\" @click=\"openActivityModal(activity)\">\r\n                                    <td class=\"col-title\">\r\n                                        <div class=\"activity-title\">\r\n                                            <h4>{{ activity.title }}</h4>\r\n                                            <p class=\"activity-description\">{{ activity.short_description || 'لا يوجد وصف' }}</p>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td class=\"col-owner\">\r\n                                        <div class=\"owner-info\">\r\n                                            <span>{{ activity.owner_name }}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td class=\"col-date\">\r\n                                        <div class=\"date-info\">\r\n                                            <span class=\"date-main\">{{ new Date(activity.activity_date).toLocaleDateString('ar-EG', { day: 'numeric', month: 'short' }) }}</span>\r\n                                            <span class=\"date-year\">{{ new Date(activity.activity_date).getFullYear() }}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td class=\"col-status\">\r\n                                        <span class=\"status-badge\" :class=\"getStatusClass(activity.state)\">\r\n                                            <div class=\"status-indicator\"></div>\r\n                                            {{ activity.state }}\r\n                                        </span>\r\n                                    </td>\r\n                                    <td class=\"col-governorate\">\r\n                                        <span class=\"governorate-name\">{{ activity.submission_info.governorate }}</span>\r\n                                    </td>\r\n                                    <td class=\"col-coordinator\">\r\n                                        <span class=\"coordinator-name\">{{ activity.submission_info.coordinator_name }}</span>\r\n                                    </td>\r\n                                    <td class=\"col-actions\" @click.stop>\r\n                                        <div class=\"action-buttons\">\r\n                                            <button @click=\"editActivity(activity)\" class=\"action-btn edit-btn\" title=\"تعديل\">\r\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                    <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\r\n                                                </svg>\r\n                                            </button>\r\n                                            <button @click=\"deleteActivity(activity.id)\" class=\"action-btn delete-btn\" title=\"حذف\">\r\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                    <path d=\"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"/>\r\n                                                </svg>\r\n                                            </button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Account Settings Modal -->\r\n    <div v-if=\"showAccountSettings\" class=\"modal-overlay\" @click=\"closeAccountSettings\">\r\n        <div class=\"modal-content account-settings-modal\" @click.stop>\r\n            <div class=\"modal-header\">\r\n                <h3>إعدادات الحساب</h3>\r\n                <button @click=\"closeAccountSettings\" class=\"close-btn\">&times;</button>\r\n            </div>\r\n            \r\n            <form @submit.prevent=\"updateAccountSettings\" class=\"account-form\">\r\n                <div class=\"form-group\">\r\n                    <label for=\"fullName\">الاسم الكامل:</label>\r\n                    <input \r\n                        type=\"text\" \r\n                        id=\"fullName\" \r\n                        v-model=\"accountForm.fullName\" \r\n                        required \r\n                        class=\"form-input\"\r\n                        placeholder=\"أدخل اسمك الكامل\"\r\n                    >\r\n                </div>\r\n                \r\n                <div class=\"form-group\">\r\n                    <label for=\"teamPin\">رمز الفريق الرقمي:</label>\r\n                    <input \r\n                        type=\"text\" \r\n                        id=\"teamPin\" \r\n                        v-model=\"accountForm.teamPin\" \r\n                        class=\"form-input\"\r\n                        placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                    >\r\n                </div>\r\n                \r\n                <div class=\"password-section\">\r\n                    <h4>تغيير كلمة المرور (اختياري)</h4>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"currentPassword\">كلمة المرور الحالية:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"currentPassword\" \r\n                            v-model=\"accountForm.currentPassword\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل كلمة المرور الحالية\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"newPassword\">كلمة المرور الجديدة:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"newPassword\" \r\n                            v-model=\"accountForm.newPassword\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل كلمة المرور الجديدة (6 أحرف على الأقل)\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"confirmPassword\">تأكيد كلمة المرور الجديدة:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"confirmPassword\" \r\n                            v-model=\"accountForm.confirmPassword\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أعد إدخال كلمة المرور الجديدة\"\r\n                        >\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"form-actions\">\r\n                    <button type=\"button\" @click=\"closeAccountSettings\" class=\"cancel-btn\">إلغاء</button>\r\n                    <button type=\"submit\" :disabled=\"updatingAccount\" class=\"save-btn\">\r\n                        <span v-if=\"updatingAccount\">جاري الحفظ...</span>\r\n                        <span v-else>حفظ التغييرات</span>\r\n                    </button>\r\n                </div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- PIN Confirmation Modal -->\r\n    <div v-if=\"showPinConfirmation\" class=\"modal-overlay\" @click=\"closePinConfirmation\">\r\n        <div class=\"modal-content pin-modal\" @click.stop>\r\n            <div class=\"modal-header\">\r\n                <h3>تأكيد العملية</h3>\r\n                <button @click=\"closePinConfirmation\" class=\"close-btn\">&times;</button>\r\n            </div>\r\n            <div class=\"modal-body\">\r\n                <p class=\"pin-message\">\r\n                    {{ pinConfirmationData.action === 'delete' ? 'لحذف هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' : 'لتعديل هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' }}\r\n                </p>\r\n                <div class=\"form-group\">\r\n                    <label for=\"confirmPin\">رمز الفريق الرقمي:</label>\r\n                    <input \r\n                        type=\"password\" \r\n                        id=\"confirmPin\"\r\n                        v-model=\"pinConfirmationData.pin\" \r\n                        placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                        @keyup.enter=\"confirmPinAction\"\r\n                        class=\"form-control\"\r\n                    >\r\n                </div>\r\n            </div>\r\n            <div class=\"modal-footer\">\r\n                <button @click=\"closePinConfirmation\" class=\"cancel-btn\">إلغاء</button>\r\n                <button @click=\"confirmPinAction\" class=\"confirm-btn\" :disabled=\"!pinConfirmationData.pin\">\r\n                    {{ pinConfirmationData.action === 'delete' ? 'حذف' : 'تعديل' }}\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Activity Details Modal -->\r\n    <div v-if=\"showActivityModal && selectedActivity\" class=\"modal-overlay\" @click=\"closeActivityModal\">\r\n        <div class=\"modal-content activity-details-modal\" @click.stop>\r\n            <div class=\"modal-header\">\r\n                <h3>تفاصيل النشاط</h3>\r\n                <button @click=\"closeActivityModal\" class=\"close-btn\">&times;</button>\r\n            </div>\r\n\r\n            <div class=\"modal-body activity-document\">\r\n                <!-- Document Header -->\r\n                <div class=\"document-header\">\r\n                    <div class=\"logo-section\">\r\n                        <div class=\"logo-container\">\r\n                            <img src=\"@/assets/scy_logo.jpg\" alt=\"شعار المجلس الأعلى للشباب\" class=\"organization-logo\" />\r\n                            <span class=\"logo-label\">شعار المجلس</span>\r\n                        </div>\r\n                        <div class=\"organization-info\">\r\n                            <h2>المجلس الأعلى للشباب</h2>\r\n                            <h3>الفريق الوطني للشباب الرقمي</h3>\r\n                        </div>\r\n                        <div class=\"logo-container\">\r\n                            <img src=\"@/assets/ndyt_logo.jpg\" alt=\"شعار الفريق الوطني للشباب الرقمي\" class=\"organization-logo\" />\r\n                            <span class=\"logo-label\">شعار الفريق</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"document-title\">\r\n                        <h1>{{ selectedActivity.title }}</h1>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Activity Details Table -->\r\n                <div class=\"details-table\">\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">الاسم</div>\r\n                        <div class=\"detail-value\">الفريق الوطني للشباب الرقمي - {{ selectedActivity.governorate }} - {{ selectedActivity.owner_name }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">اسم النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.title }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_idea\">\r\n                        <div class=\"detail-label\">فكرة النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.activity_idea }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_goals && selectedActivity.activity_goals.length > 0\">\r\n                        <div class=\"detail-label\">أهداف النشاط</div>\r\n                        <div class=\"detail-value\">\r\n                            <ul class=\"goals-list\">\r\n                                <li v-for=\"goal in selectedActivity.activity_goals\" :key=\"goal.text\">- {{ goal.text }}</li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.target_groups && selectedActivity.target_groups.length > 0\">\r\n                        <div class=\"detail-label\">الفئة المستهدفة</div>\r\n                        <div class=\"detail-value\">\r\n                            <ul class=\"target-groups-list\">\r\n                                <li v-for=\"group in selectedActivity.target_groups\" :key=\"group.text\">{{ group.text }}</li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.audience_count\">\r\n                        <div class=\"detail-label\">عدد المستهدفين</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.audience_count }} شخص</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">تاريخ النشاط</div>\r\n                        <div class=\"detail-value\">{{ formatDate(selectedActivity.activity_date) }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_time\">\r\n                        <div class=\"detail-label\">وقت النشاط</div>\r\n                        <div class=\"detail-value\">{{ formatTime(selectedActivity.activity_time) }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_location\">\r\n                        <div class=\"detail-label\">مكان النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.activity_location }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_duration\">\r\n                        <div class=\"detail-label\">مدة النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.activity_duration }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_budget\">\r\n                        <div class=\"detail-label\">ميزانية النشاط</div>\r\n                        <div class=\"detail-value\">{{ formatCurrency(selectedActivity.activity_budget) }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.budget_details && selectedActivity.budget_details.length > 0\">\r\n                        <div class=\"detail-label\">تفاصيل الصرف</div>\r\n                        <div class=\"detail-value\">\r\n                            <table class=\"budget-table\">\r\n                                <thead>\r\n                                    <tr>\r\n                                        <th>المادة</th>\r\n                                        <th>النوع</th>\r\n                                        <th>العدد</th>\r\n                                        <th>السعر</th>\r\n                                        <th>المجموع</th>\r\n                                    </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                    <tr v-for=\"detail in selectedActivity.budget_details\" :key=\"detail.name\">\r\n                                        <td>{{ detail.name }}</td>\r\n                                        <td>{{ detail.type }}</td>\r\n                                        <td>{{ detail.amount }}</td>\r\n                                        <td>{{ formatCurrency(detail.price) }}</td>\r\n                                        <td>{{ formatCurrency(detail.budgetPrice) }}</td>\r\n                                    </tr>\r\n                                </tbody>\r\n                                <tfoot v-if=\"selectedActivity.total_budget\">\r\n                                    <tr class=\"total-row\">\r\n                                        <td colspan=\"4\"><strong>الإجمالي</strong></td>\r\n                                        <td><strong>{{ formatCurrency(selectedActivity.total_budget) }}</strong></td>\r\n                                    </tr>\r\n                                </tfoot>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_levels && selectedActivity.activity_levels.length > 0\">\r\n                        <div class=\"detail-label\">مراحل تنفيذ النشاط</div>\r\n                        <div class=\"detail-value\">\r\n                            <ul class=\"levels-list\">\r\n                                <li v-for=\"level in selectedActivity.activity_levels\" :key=\"level.description\">{{ level.description }}</li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">حالة النشاط</div>\r\n                        <div class=\"detail-value\">\r\n                            <span class=\"status-badge\" :class=\"getStatusClass(selectedActivity.state)\">\r\n                                {{ selectedActivity.state }}\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">المحافظة</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.governorate }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">منسق المحافظة</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.coordinator_name }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">تاريخ الإرسال</div>\r\n                        <div class=\"detail-value\">{{ formatDate(selectedActivity.created_at) }}</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"modal-footer\">\r\n                <button @click=\"printActivity\" class=\"print-btn\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\" style=\"margin-left: 8px;\">\r\n                        <path d=\"M18,3H6V7H18M19,12A1,1 0 0,1 18,11A1,1 0 0,1 19,10A1,1 0 0,1 20,11A1,1 0 0,1 19,12M16,19H8V14H16M19,8H5A3,3 0 0,0 2,11V17H6V21H18V17H22V11A3,3 0 0,0 19,8Z\"/>\r\n                    </svg>\r\n                    طباعة\r\n                </button>\r\n                <button @click=\"exportToDocx\" class=\"export-docx-btn\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\" style=\"margin-left: 8px;\">\r\n                        <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                    </svg>\r\n                    تصدير كملف Word\r\n                </button>\r\n                <button @click=\"closeActivityModal\" class=\"close-modal-btn\">\r\n                    إغلاق\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<style>\r\nbody {\r\n    background-color: #121212;\r\n    color: #e0e0e0;\r\n    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.navbar {\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    border: 2px solid #3a3a5e;\r\n    border-radius: 12px;\r\n    margin: 16px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.navbar-content {\r\n    padding: 16px 24px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    flex-wrap: wrap;\r\n    gap: 16px;\r\n}\r\n\r\n.navbar-text {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n    flex: 1;\r\n}\r\n\r\n.org-name, .team-name {\r\n    font-weight: 700;\r\n    font-size: clamp(16px, 3vw, 24px);\r\n    color: #f5f5f5;\r\n    text-align: center;\r\n    line-height: 1.4;\r\n}\r\n\r\n.logo {\r\n    height: clamp(50px, 8vw, 70px);\r\n    width: auto;\r\n    border: 2px solid #4a5568;\r\n    border-radius: 50%;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.logo:hover {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.user-section {\r\n    position: relative;\r\n    z-index: 100;\r\n}\r\n\r\n.user-button {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    border-radius: 8px;\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.user-button:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.user-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n    background: linear-gradient(135deg, #667eea, #764ba2);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n}\r\n\r\n.user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n    color: #f5f5f5;\r\n    white-space: nowrap;\r\n}\r\n\r\n.dropdown-arrow {\r\n    transition: transform 0.3s ease;\r\n    color: #a0aec0;\r\n}\r\n\r\n.user-button:hover .dropdown-arrow {\r\n    transform: rotate(180deg);\r\n}\r\n\r\n.user-menu {\r\n    position: absolute;\r\n    top: 100%;\r\n    right: auto;\r\n    left: 0;\r\n    margin-top: 8px;\r\n    background: #1a202c;\r\n    border: 2px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 8px;\r\n    min-width: 180px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n    overflow: hidden;\r\n    animation: slideDown 0.3s ease;\r\n}\r\n\r\n/* Navigation Buttons */\r\n.nav-buttons {\r\n    display: flex;\r\n    gap: 16px;\r\n    align-items: center;\r\n}\r\n\r\n/* Arabic RTL alignment for navbar actions */\r\n.nav-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.nav-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    min-width: 160px; /* unified width with global buttons */\r\n    justify-content: center;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 8px;\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    font-size: 14px;\r\n    letter-spacing: 0.2px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n}\r\n\r\n/* Compact button utility for small actions */\r\n.btn-compact {\r\n    min-width: 96px;\r\n    height: 40px;\r\n    padding: 8px 14px;\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.nav-btn:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    border-color: rgba(255, 255, 255, 0.3);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.nav-btn.active {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    border-color: #4f46e5;\r\n    color: #ffffff;\r\n    box-shadow: 0 2px 12px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.nav-btn.active:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n    margin: 24px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n}\r\n\r\n.submit-view {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n}\r\n\r\n.submit-header {\r\n    text-align: center;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n}\r\n\r\n.submit-header h2 {\r\n    color: #ffffff;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n}\r\n\r\n/* View Activities */\r\n.view-activities {\r\n    margin-top: 32px;\r\n    padding: 0 8px;\r\n}\r\n\r\n.view-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 32px 24px;\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(124, 58, 237, 0.15));\r\n    border: 2px solid rgba(79, 70, 229, 0.3);\r\n    border-radius: 16px;\r\n    margin-bottom: 32px;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.view-header::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed, #4f46e5);\r\n}\r\n\r\n.view-header-content {\r\n    text-align: center;\r\n    flex: 1;\r\n}\r\n\r\n.view-title {\r\n    color: #ffffff;\r\n    font-size: 28px;\r\n    font-weight: 700;\r\n    margin: 0 0 12px 0;\r\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.view-description {\r\n    color: #e2e8f0;\r\n    font-size: 16px;\r\n    margin: 0;\r\n    opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.export-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #f59e0b, #d97706);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\r\n    min-width: 120px;\r\n    height: auto;\r\n}\r\n\r\n.export-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d97706, #b45309);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);\r\n}\r\n\r\n.export-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n    background: linear-gradient(135deg, #6b7280, #4b5563);\r\n}\r\n\r\n.refresh-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #10b981, #059669);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\r\n    min-width: 100px;\r\n    height: auto;\r\n}\r\n\r\n.refresh-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #059669, #047857);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\r\n}\r\n\r\n.refresh-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.refresh-btn svg.spinning {\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    from { transform: rotate(0deg); }\r\n    to { transform: rotate(360deg); }\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 24px;\r\n}\r\n\r\n.activities-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n    padding: 0 4px;\r\n}\r\n\r\n.activity-card {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 16px;\r\n    padding: 24px;\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed);\r\n    transform: scaleX(0);\r\n    transition: transform 0.4s ease;\r\n}\r\n\r\n.activity-card:hover {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);\r\n    border-color: rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.activity-card:hover::before {\r\n    transform: scaleX(1);\r\n}\r\n\r\n.activity-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 2px solid rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.activity-header h4 {\r\n    color: #ffffff;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n    line-height: 1.3;\r\n    flex: 1;\r\n    margin-right: 16px;\r\n}\r\n\r\n.activity-actions {\r\n    display: flex;\r\n    gap: 10px;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-btn, .delete-btn {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 40px;\r\n    min-width: 120px;\r\n    padding: 10px 16px;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: none;\r\n    text-align: center;\r\n}\r\n\r\n.edit-btn {\r\n    background: linear-gradient(135deg, #3182ce, #2c5aa0);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(49, 130, 206, 0.3);\r\n}\r\n\r\n.edit-btn:hover {\r\n    background: linear-gradient(135deg, #2c5aa0, #2a4d8a);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);\r\n}\r\n\r\n.delete-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);\r\n}\r\n\r\n.delete-btn:hover {\r\n    background: linear-gradient(135deg, #c53030, #a02626);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.activity-details {\r\n    margin-top: 16px;\r\n}\r\n\r\n.activity-details p {\r\n    color: #e2e8f0;\r\n    font-size: 15px;\r\n    margin: 12px 0;\r\n    line-height: 1.6;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.activity-details strong {\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    min-width: 100px;\r\n    display: inline-block;\r\n}\r\n\r\n.status-badge {\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    text-align: center;\r\n    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);\r\n    border: 1px solid rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.no-activities-message, .loading-message {\r\n    text-align: center;\r\n    padding: 48px 24px;\r\n    color: #cbd5e0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));\r\n    border: 2px solid rgba(255, 255, 255, 0.1);\r\n    border-radius: 16px;\r\n    margin: 24px 0;\r\n}\r\n\r\n.loading-message {\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));\r\n    border-color: rgba(79, 70, 229, 0.2);\r\n    animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n    0%, 100% { opacity: 1; }\r\n    50% { opacity: 0.7; }\r\n}\r\n\r\n@keyframes slideDown {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-10px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n.user-menu-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    padding: 12px 16px;\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-menu-item:hover {\r\n    background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-menu-item svg {\r\n    color: #e53e3e;\r\n}\r\n\r\nspan {\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n}\r\n\r\n.view {\r\n    padding: 16px;\r\n    background: linear-gradient(135deg, #1e1e2e, #2a2a3e);\r\n    border: 1px solid #3a3a4e;\r\n    border-radius: 20px;\r\n    max-width: 90%;\r\n    margin: 16px auto;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\nbutton {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    height: 48px;\r\n    min-width: 160px;\r\n    border: none;\r\n    border-radius: 12px;\r\n    padding: 12px 24px;\r\n    cursor: pointer;\r\n    font-weight: 700;\r\n    font-size: 15px;\r\n    letter-spacing: 0.2px;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n/* Make icon+label buttons look consistent */\r\nbutton svg { flex-shrink: 0; }\r\nbutton span { display: inline-block; }\r\n\r\n/* Inputs: improve placeholder visibility */\r\ninput::placeholder,\r\nselect::placeholder,\r\ntextarea::placeholder {\r\n    color: #cbd5e1; /* brighter placeholder */\r\n    opacity: 1;\r\n}\r\n\r\n/* Edge/Firefox vendor prefixes */\r\ninput::-ms-input-placeholder { color: #cbd5e1; }\r\ninput::-webkit-input-placeholder { color: #cbd5e1; }\r\ntextarea::-webkit-input-placeholder { color: #cbd5e1; }\r\nselect::-ms-input-placeholder { color: #cbd5e1; }\r\n\r\nbutton:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\nbutton:active {\r\n    transform: translateY(0);\r\n}\r\n\r\nbutton span {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.info {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n}\r\n\r\n.base-info-container,\r\n.activities-info-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    direction: rtl;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n    padding: 24px;\r\n}\r\n\r\n.base-info-label {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    color: #f1f5f9;\r\n    margin-bottom: 16px;\r\n    text-align: right;\r\n    border-bottom: 2px solid #4f46e5;\r\n    padding-bottom: 8px;\r\n}\r\n\r\n.field-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 8px;\r\n    text-align: right;\r\n    display: block;\r\n}\r\n\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\ninput[type=\"time\"],\r\ninput[type='stat-number'],\r\ntextarea, select {\r\n    direction: rtl;\r\n    width: 100%;\r\n    max-width: 400px;\r\n    margin: 8px 0;\r\n    padding: 12px 16px;\r\n    background: rgba(255, 255, 255, 0.12);\r\n    color: #ffffff;\r\n    border: 2px solid rgba(255, 255, 255, 0.25);\r\n    border-radius: 8px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\ninput[type='stat-number'] {\r\n    width: 300px;\r\n}\r\n\r\ninput[type=\"text\"]:focus,\r\ninput[type=\"date\"]:focus,\r\nselect:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);\r\n    background: rgba(255, 255, 255, 0.18);\r\n    color: #ffffff;\r\n}\r\n\r\nselect {\r\n    cursor: pointer;\r\n}\r\n\r\nselect option {\r\n    background: #1a1a2e;\r\n    color: #f0f0f0;\r\n    padding: 8px;\r\n}\r\n.activity-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 8px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-item:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.activity-file-input {\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 12px;\r\n    width: fit-content;\r\n    max-width: 200px;\r\n    text-align: center;\r\n    padding: 12px 16px;\r\n    font-size: 14px;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    color: #f0f0f0;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    backdrop-filter: blur(10px);\r\n    align-self: flex-start;\r\n}\r\n\r\n.activity-file-input:hover {\r\n    background: rgba(255, 255, 255, 0.15);\r\n    border-color: #4f46e5;\r\n}\r\n\r\n.activity-delete-button {\r\n    background: linear-gradient(135deg, #ef4444, #dc2626);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    margin: 8px 0;\r\n    padding: 10px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    font-weight: 600;\r\n    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.activity-delete-button:hover {\r\n    background: linear-gradient(135deg, #f87171, #ef4444);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .navbar-text {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 18px;\r\n    }\r\n    \r\n    .user-button {\r\n        width: 100%;\r\n        justify-content: center;\r\n    }\r\n    \r\n    .info {\r\n        grid-template-columns: 1fr;\r\n        gap: 16px;\r\n    }\r\n    \r\n    .base-info-container,\r\n    .activities-info-container {\r\n        padding: 16px;\r\n    }\r\n    \r\n    .view {\r\n        margin: 8px;\r\n        padding: 12px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: 8px;\r\n    }\r\n    \r\n    .navbar-content {\r\n        padding: 12px 16px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .logo {\r\n        height: 50px;\r\n    }\r\n    \r\n    .user-name {\r\n        font-size: 12px;\r\n    }\r\n    \r\n    input[type=\"text\"],\r\n    input[type=\"date\"],\r\n    select {\r\n        font-size: 16px; /* Prevents zoom on iOS */\r\n    }\r\n}\r\n\r\n/* RTL Improvements */\r\n.view {\r\n    direction: rtl;\r\n}\r\n\r\n.navbar-content {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.navbar-text {\r\n    flex-direction: row-reverse;\r\n    text-align: right;\r\n}\r\n\r\n.nav-actions .nav-btn {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.view-header-content,\r\n.view-title,\r\n.view-description {\r\n    text-align: right;\r\n}\r\n\r\n.activities-list {\r\n    direction: rtl;\r\n}\r\n\r\nlabel {\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 4px;\r\n    display: block;\r\n}\r\n\r\n/* Inputs RTL */\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\nselect,\r\ntextarea {\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n/* Fine color and spacing tweaks for Arabic */\r\n.navbar {\r\n    border-color: #4b5563;\r\n}\r\n.nav-btn {\r\n    letter-spacing: 0.2px; /* tighter Arabic rhythm */\r\n}\r\n.view-header {\r\n    border-color: rgba(79, 70, 229, 0.35);\r\n}\r\n.activity-card {\r\n    padding-inline: 24px;\r\n}\r\n.activity-header h4 {\r\n    margin-left: 0;\r\n    margin-right: 15px; /* move spacing to the right for RTL */\r\n}\r\n.activity-details,\r\n.activity-details p {\r\n    text-align: right;\r\n}\r\n\r\n.splitter {\r\n    height: 2px;\r\n    background: linear-gradient(90deg, transparent, #4f46e5, transparent);\r\n    margin: 24px 0;\r\n    border-radius: 1px;\r\n}\r\n\r\n/* My Activities Section Styles */\r\n.my-activities-section {\r\n    margin: 30px 0;\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n.toggle-activities-btn {\r\n    width: 100%;\r\n    padding: 15px 20px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.toggle-activities-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.toggle-icon {\r\n    font-size: 14px;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 15px;\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.loading-message, .no-activities-message {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n}\r\n\r\n/* Activities Container */\r\n.activities-container {\r\n    margin-top: 20px;\r\n    padding: 0;\r\n}\r\n\r\n/* Loading and Empty States */\r\n.loading-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 60px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    gap: 16px;\r\n}\r\n\r\n.loading-spinner {\r\n    width: 40px;\r\n    height: 40px;\r\n    border: 3px solid rgba(255, 255, 255, 0.1);\r\n    border-top: 3px solid #4f46e5;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n}\r\n\r\n.no-activities-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n    color: #8892b0;\r\n    gap: 16px;\r\n}\r\n\r\n.empty-icon {\r\n    opacity: 0.6;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.no-activities-message h3 {\r\n    margin: 0;\r\n    color: #e6f1ff;\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n}\r\n\r\n.no-activities-message p {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    opacity: 0.8;\r\n}\r\n\r\n/* Edit Modal - Clean Flexbox Design */\r\n.edit-modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.75);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.edit-modal {\r\n    background: #1e293b;\r\n    border: 1px solid #334155;\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    max-width: 600px;\r\n    max-height: calc(100vh - 40px);\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.edit-modal-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20px 24px;\r\n    border-bottom: 1px solid #334155;\r\n    background: #0f172a;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-modal-header h3 {\r\n    margin: 0;\r\n    color: #f1f5f9;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: #64748b;\r\n    cursor: pointer;\r\n    padding: 8px;\r\n    border-radius: 6px;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.edit-form {\r\n    padding: 24px;\r\n    direction: rtl;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    overflow-y: auto;\r\n    flex: 1;\r\n    min-height: 0;\r\n}\r\n\r\n.form-row {\r\n    display: flex;\r\n    gap: 16px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-row.single {\r\n    flex-direction: column;\r\n}\r\n\r\n.form-row.double {\r\n    flex-direction: row;\r\n}\r\n\r\n.form-row.double .form-group {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* Table Styles */\r\n.activities-table-container {\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 16px;\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n    backdrop-filter: blur(10px);\r\n    overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n    padding: 24px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.search-container {\r\n    margin-bottom: 24px;\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.search-box {\r\n    position: relative;\r\n    width: 100%;\r\n    max-width: 500px;\r\n}\r\n\r\n.search-input {\r\n    width: 100%;\r\n    padding: 12px 16px 12px 56px;\r\n    border: 2px solid rgba(255, 255, 255, 0.1);\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    color: white;\r\n    font-size: 16px;\r\n    transition: border-color 0.3s ease, background-color 0.3s ease;\r\n    direction: rtl;\r\n    text-align: right;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.search-input::placeholder {\r\n    color: rgba(255, 255, 255, 0.6);\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n.search-input:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);\r\n}\r\n\r\n.search-icon-btn {\r\n    position: absolute;\r\n    left: 12px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    background: none;\r\n    border: none;\r\n    cursor: pointer;\r\n    padding: 6px;\r\n    border-radius: 6px;\r\n    width: 28px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.search-icon-btn.clickable:hover {\r\n    color: white;\r\n    background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.search-icon {\r\n    position: relative;\r\n}\r\n\r\n.clear-search-btn {\r\n    position: absolute;\r\n    left: 24px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    background: none;\r\n    border: none;\r\n    color: rgba(255, 255, 255, 0.6);\r\n    cursor: pointer;\r\n    padding: 6px;\r\n    border-radius: 6px;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 28px;\r\n    height: 28px;\r\n}\r\n\r\n\r\n.table-stats {\r\n    display: flex;\r\n    gap: 32px;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.stat-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.stat-item.clickable {\r\n    cursor: pointer;\r\n    padding: 12px 16px;\r\n    border-radius: 12px;\r\n    transition: all 0.3s ease;\r\n    border: 2px solid transparent;\r\n}\r\n\r\n.stat-item.clickable:hover {\r\n    background: rgba(79, 70, 229, 0.1);\r\n    border-color: rgba(79, 70, 229, 0.3);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.stat-item.selected {\r\n    background: rgba(79, 70, 229, 0.2);\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.stat-item.selected .stat-number {\r\n    color: #6366f1;\r\n}\r\n\r\n.stat-item.selected .stat-label {\r\n    color: #c7d2fe;\r\n}\r\n\r\n.stat-number {\r\n    font-size: 32px;\r\n    font-weight: 700;\r\n    color: #4f46e5;\r\n    line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n    font-size: 14px;\r\n    color: #8892b0;\r\n    font-weight: 500;\r\n}\r\n\r\n.table-wrapper {\r\n    overflow-x: auto;\r\n}\r\n\r\n.activities-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    direction: rtl;\r\n}\r\n\r\n.activities-table th {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: 16px 12px;\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #e6f1ff;\r\n    font-size: 14px;\r\n    border-bottom: 2px solid rgba(255, 255, 255, 0.1);\r\n    white-space: nowrap;\r\n}\r\n\r\n.activities-table td {\r\n    padding: 16px 12px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\r\n    vertical-align: middle;\r\n}\r\n\r\n.activity-row {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-row:hover {\r\n    background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n/* Column Specific Styles */\r\n.col-title {\r\n    min-width: 250px;\r\n}\r\n\r\n.activity-title h4 {\r\n    margin: 0 0 4px 0;\r\n    color: #e6f1ff;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    line-height: 1.3;\r\n}\r\n\r\n.activity-description {\r\n    margin: 0;\r\n    color: #8892b0;\r\n    font-size: 13px;\r\n    line-height: 1.4;\r\n    display: -webkit-box;\r\n    line-clamp: 2;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.owner-info {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.date-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 2px;\r\n}\r\n\r\n.date-main {\r\n    color: #e6f1ff;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n}\r\n\r\n.date-year {\r\n    color: #8892b0;\r\n    font-size: 12px;\r\n}\r\n\r\n.status-badge {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    white-space: nowrap;\r\n}\r\n\r\n.status-indicator {\r\n    width: 6px;\r\n    height: 6px;\r\n    border-radius: 50%;\r\n    background: currentColor;\r\n}\r\n\r\n/* Status Colors */\r\n.status-executed-paid {\r\n    background: rgba(34, 197, 94, 0.2);\r\n    color: #22c55e;\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n}\r\n\r\n.status-executed-unpaid {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.status-accepted {\r\n    background: rgba(16, 185, 129, 0.2);\r\n    color: #10b981;\r\n    border: 1px solid rgba(16, 185, 129, 0.3);\r\n}\r\n\r\n.status-rejected {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.status-needs-edit {\r\n    background: rgba(245, 158, 11, 0.2);\r\n    color: #f59e0b;\r\n    border: 1px solid rgba(245, 158, 11, 0.3);\r\n}\r\n\r\n.status-paid-not-executed {\r\n    background: rgba(168, 85, 247, 0.2);\r\n    color: #a855f7;\r\n    border: 1px solid rgba(168, 85, 247, 0.3);\r\n}\r\n\r\n.status-accepted-unpaid {\r\n    background: rgba(6, 182, 212, 0.2);\r\n    color: #06b6d4;\r\n    border: 1px solid rgba(6, 182, 212, 0.3);\r\n}\r\n\r\n.status-sent {\r\n    background: rgba(139, 92, 246, 0.2);\r\n    color: #8b5cf6;\r\n    border: 1px solid rgba(139, 92, 246, 0.3);\r\n}\r\n\r\n.governorate-name, .coordinator-name {\r\n    color: #ccd6f6;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 36px;\r\n    height: 36px;\r\n    border: none;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.edit-btn {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.action-btn.edit-btn:hover {\r\n    background: rgba(59, 130, 246, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n.action-btn.delete-btn {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.action-btn.delete-btn:hover {\r\n    background: rgba(239, 68, 68, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n/* File Download Styles */\r\n.files-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n    min-width: 120px;\r\n}\r\n\r\n.file-buttons {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.file-download-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 10px;\r\n    background: rgba(34, 197, 94, 0.15);\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n    border-radius: 6px;\r\n    color: #22c55e;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n    min-height: 28px;\r\n    max-width: 140px;\r\n}\r\n\r\n.file-download-btn:hover {\r\n    background: rgba(34, 197, 94, 0.25);\r\n    border-color: rgba(34, 197, 94, 0.5);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);\r\n}\r\n\r\n.file-download-btn svg {\r\n    flex-shrink: 0;\r\n    color: #22c55e;\r\n}\r\n\r\n.file-name {\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.no-files {\r\n    color: #64748b;\r\n    font-size: 12px;\r\n    font-style: italic;\r\n    text-align: center;\r\n    padding: 8px;\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from { opacity: 0; }\r\n    to { opacity: 1; }\r\n}\r\n\r\n@keyframes slideUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Edit Form Styles */\r\n.edit-form {\r\n    direction: rtl;\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-group label {\r\n    color: #cbd5e1;\r\n    font-weight: 500;\r\n    font-size: 14px;\r\n    margin-bottom: 0;\r\n    text-align: right;\r\n}\r\n\r\n.form-input, .form-textarea, .form-select {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    border: 1px solid #475569;\r\n    border-radius: 6px;\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n    font-size: 14px;\r\n    font-family: inherit;\r\n    transition: all 0.2s ease;\r\n    direction: rtl;\r\n    text-align: right;\r\n    box-sizing: border-box;\r\n    outline: none;\r\n}\r\n\r\n.form-input:focus, .form-textarea:focus, .form-select:focus {\r\n    border-color: #3b82f6;\r\n    background: #1e293b;\r\n    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);\r\n}\r\n\r\n.form-input::placeholder, .form-textarea::placeholder {\r\n    color: #94a3b8;\r\n}\r\n\r\n.form-select {\r\n    cursor: pointer;\r\n    appearance: none;\r\n    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4 5\"><path fill=\"%23f1f5f9\" d=\"M2 0L0 2h4zm0 5L0 3h4z\"/></svg>');\r\n    background-repeat: no-repeat;\r\n    background-position: left 12px center;\r\n    background-size: 12px;\r\n    padding-left: 32px;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n.form-textarea {\r\n    min-height: 100px;\r\n    resize: vertical;\r\n    line-height: 1.5;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 24px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #475569;\r\n}\r\n\r\n.save-btn, .cancel-btn {\r\n    padding: 10px 20px;\r\n    border-radius: 6px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    border: 1px solid transparent;\r\n    outline: none;\r\n}\r\n\r\n.save-btn {\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.save-btn:hover {\r\n    background: #2563eb;\r\n    border-color: #2563eb;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #94a3b8;\r\n    border-color: #475569;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n}\r\n\r\n    /* Responsive Design for My Activities */\r\n    @media (max-width: 768px) {\r\n        .view-activities {\r\n            padding: 0 4px;\r\n        }\r\n        \r\n        .view-header {\r\n            flex-direction: column;\r\n            gap: 16px;\r\n            padding: 24px 16px;\r\n            margin-bottom: 24px;\r\n        }\r\n        \r\n        .view-header-content {\r\n            order: 1;\r\n        }\r\n        \r\n        .refresh-btn {\r\n            order: 2;\r\n            align-self: center;\r\n            min-width: 120px;\r\n            padding: 10px 16px;\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .view-title {\r\n            font-size: 24px;\r\n        }\r\n        \r\n        .view-description {\r\n            font-size: 14px;\r\n        }\r\n        \r\n        .activities-grid {\r\n            grid-template-columns: 1fr;\r\n            gap: 16px;\r\n            padding: 0;\r\n        }\r\n        \r\n        .activity-card {\r\n            padding: 20px;\r\n            margin: 0;\r\n        }\r\n        \r\n        .activity-header {\r\n            flex-direction: column;\r\n            gap: 12px;\r\n            align-items: stretch;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 18px;\r\n            margin-right: 0;\r\n        }\r\n        \r\n        .activity-actions {\r\n            justify-content: flex-end;\r\n            gap: 8px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 8px 12px;\r\n            font-size: 13px;\r\n            min-width: 96px;\r\n            height: 40px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 14px;\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 4px;\r\n        }\r\n        \r\n        .activity-details strong {\r\n            min-width: auto;\r\n        }\r\n        \r\n        .no-activities-message, .loading-message {\r\n            padding: 32px 16px;\r\n            font-size: 16px;\r\n        }\r\n    }\r\n    \r\n    @media (max-width: 480px) {\r\n        .activity-card {\r\n            padding: 12px;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 16px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 5px 10px;\r\n            font-size: 11px;\r\n        }\r\n        \r\n        .edit-modal {\r\n            width: 95%;\r\n            max-width: none;\r\n            margin: 20px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .edit-modal-header {\r\n            padding: 20px 24px;\r\n        }\r\n        \r\n        .edit-form {\r\n            padding: 24px;\r\n            gap: 20px;\r\n        }\r\n        \r\n        .form-row {\r\n            grid-template-columns: 1fr;\r\n            gap: 20px;\r\n            padding: 12px;\r\n            margin-bottom: 16px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .form-row:hover {\r\n            transform: none;\r\n            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n        }\r\n        \r\n        .form-input, .form-textarea, .form-select {\r\n            padding: 16px 20px;\r\n            font-size: 16px;\r\n            border-radius: 14px;\r\n        }\r\n        \r\n        .form-textarea {\r\n            min-height: 100px;\r\n            padding-top: 18px;\r\n            padding-bottom: 18px;\r\n        }\r\n        \r\n        .form-group label {\r\n            font-size: 13px;\r\n            margin-right: 2px;\r\n        }\r\n        \r\n        .form-select {\r\n            padding-left: 35px;\r\n            background-position: calc(100% - 18px) calc(1em + 2px), \r\n                                 calc(100% - 13px) calc(1em + 2px);\r\n        }\r\n        \r\n        .form-actions {\r\n            flex-direction: column-reverse;\r\n            align-items: stretch;\r\n            gap: 12px;\r\n            padding: 20px 0 0 0;\r\n        }\r\n        \r\n        .save-btn, .cancel-btn {\r\n            width: 100%;\r\n            padding: 14px;\r\n            min-width: auto;\r\n        }\r\n    }\r\n\r\n/* Modal Overlay Styles */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    backdrop-filter: blur(5px);\r\n}\r\n\r\n.modal-content {\r\n    background: #1a1a2e;\r\n    border-radius: 20px;\r\n    width: 90%;\r\n    max-width: 500px;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\r\n    animation: modalSlideIn 0.3s ease;\r\n    border: 2px solid #4facfe;\r\n    position: relative;\r\n    z-index: 1001;\r\n    padding: 10px;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-50px) scale(0.9);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0) scale(1);\r\n    }\r\n}\r\n\r\n/* Modal Header Styles */\r\n.modal-header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    border-radius: 20px 20px 0 0;\r\n}\r\n\r\n.modal-header h3 {\r\n    margin: 0;\r\n    font-size: 1.3rem;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: white;\r\n    font-size: 1.5rem;\r\n    cursor: pointer;\r\n    padding: 0;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: background 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* Account Settings Modal Styles */\r\n.account-settings-modal {\r\n    max-width: 600px;\r\n    width: 90%;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    padding: 10px;\r\n}\r\n\r\n.account-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n    display: block;\r\n    margin-bottom: 8px;\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    border-radius: 8px;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n    transition: all 0.3s ease;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.form-input:focus {\r\n    outline: none;\r\n    border-color: #4facfe;\r\n    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);\r\n    background: #374151;\r\n}\r\n\r\n.form-input::placeholder {\r\n    color: #a0aec0;\r\n}\r\n\r\n.password-section {\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.password-section h4 {\r\n    margin: 0 0 20px 0;\r\n    color: #4facfe;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.save-btn {\r\n    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.save-btn:hover:not(:disabled) {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);\r\n}\r\n\r\n.save-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n    color: #f5f5f5;\r\n}\r\n\r\n/* Responsive styles for account modal */\r\n@media (max-width: 768px) {\r\n    .account-settings-modal {\r\n        width: 95%;\r\n        margin: 20px;\r\n    }\r\n    \r\n    .form-actions {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .save-btn,\r\n    .cancel-btn {\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n/* Clickable Row Styles */\r\n.clickable-row {\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.clickable-row:hover {\r\n    background-color: rgba(74, 85, 104, 0.1);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.clickable-row:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n/* PIN Confirmation Modal Styles */\r\n.pin-modal {\r\n    max-width: 450px;\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    border: 2px solid #4a5568;\r\n    border-radius: 16px;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.pin-modal .modal-header {\r\n    background: linear-gradient(135deg, #2d3748, #4a5568);\r\n    border-bottom: 2px solid #4a5568;\r\n    border-radius: 14px 14px 0 0;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.pin-modal .modal-header h3 {\r\n    color: #4fc3f7;\r\n    margin: 0;\r\n    font-size: 1.4rem;\r\n    font-weight: 600;\r\n}\r\n\r\n.pin-modal .modal-body {\r\n    padding: 25px;\r\n}\r\n\r\n.pin-message {\r\n    color: #e2e8f0;\r\n    font-size: 1.1rem;\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n    line-height: 1.6;\r\n}\r\n\r\n.pin-modal .form-group {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.pin-modal .form-group label {\r\n    color: #4fc3f7;\r\n    font-weight: 600;\r\n    margin-bottom: 8px;\r\n    display: block;\r\n}\r\n\r\n.pin-modal .form-control {\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    color: #e2e8f0;\r\n    padding: 12px 16px;\r\n    border-radius: 8px;\r\n    font-size: 1rem;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.pin-modal .form-control:focus {\r\n    border-color: #4fc3f7;\r\n    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);\r\n    outline: none;\r\n}\r\n\r\n.pin-modal .modal-footer {\r\n    padding: 20px 25px;\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.confirm-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.confirm-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #c53030, #9c2626);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.confirm-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.pin-modal .cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 10px 20px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.pin-modal .cancel-btn:hover {\r\n    background: #4a5568;\r\n    color: #e2e8f0;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* Activity Details Modal Styles */\r\n.activity-details-modal {\r\n    max-width: 900px;\r\n    width: 95%;\r\n    max-height: 90vh;\r\n    background: #ffffff;\r\n    color: #333333;\r\n    border-radius: 16px;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);\r\n    overflow: hidden;\r\n    position: relative;\r\n    z-index: 1001;\r\n}\r\n\r\n/* Enhanced modal overlay to prevent background scrolling */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-details-modal .modal-header {\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    color: #ffffff;\r\n    padding: 20px 30px;\r\n    border-bottom: 2px solid #4a5568;\r\n}\r\n\r\n.activity-details-modal .modal-header h3 {\r\n    margin: 0;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n}\r\n\r\n.activity-details-modal .modal-body {\r\n    padding: 0;\r\n    max-height: calc(90vh - 140px);\r\n    overflow-y: auto;\r\n}\r\n\r\n.activity-document {\r\n    padding: 30px;\r\n    font-family: 'Arial', sans-serif;\r\n    line-height: 1.6;\r\n}\r\n\r\n.document-header {\r\n    text-align: center;\r\n    margin-bottom: 40px;\r\n    border-bottom: 3px solid #1a1a2e;\r\n    padding-bottom: 30px;\r\n}\r\n\r\n.logo-section {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.logo-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.organization-logo {\r\n    width: 80px;\r\n    height: 80px;\r\n    object-fit: contain;\r\n    border-radius: 8px;\r\n    border: 2px solid #1a1a2e;\r\n    background: #ffffff;\r\n    padding: 4px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.logo-label {\r\n    font-size: 12px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n    text-align: center;\r\n}\r\n\r\n.organization-info h2 {\r\n    margin: 0;\r\n    font-size: 28px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n}\r\n\r\n.organization-info h3 {\r\n    margin: 5px 0 0 0;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #4a5568;\r\n}\r\n\r\n.document-title h1 {\r\n    margin: 0;\r\n    font-size: 32px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n    text-decoration: underline;\r\n}\r\n\r\n.details-table {\r\n    width: 100%;\r\n}\r\n\r\n.detail-row {\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    border-bottom: 1px solid #e2e8f0;\r\n    padding-bottom: 15px;\r\n}\r\n\r\n.detail-label {\r\n    flex: 0 0 200px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n    padding-right: 20px;\r\n    font-size: 16px;\r\n}\r\n\r\n.detail-value {\r\n    flex: 1;\r\n    color: #4a5568;\r\n    font-size: 16px;\r\n}\r\n\r\n.goals-list, .target-groups-list, .levels-list {\r\n    margin: 0;\r\n    padding-right: 20px;\r\n}\r\n\r\n.goals-list li, .target-groups-list li, .levels-list li {\r\n    margin-bottom: 8px;\r\n    color: #4a5568;\r\n}\r\n\r\n.budget-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    margin-top: 10px;\r\n    border: 2px solid #1a1a2e;\r\n}\r\n\r\n.budget-table th,\r\n.budget-table td {\r\n    border: 1px solid #cbd5e0;\r\n    padding: 12px;\r\n    text-align: center;\r\n}\r\n\r\n.budget-table th {\r\n    background: #1a1a2e;\r\n    color: #ffffff;\r\n    font-weight: bold;\r\n}\r\n\r\n.budget-table tbody tr:nth-child(even) {\r\n    background: #f8f9fa;\r\n}\r\n\r\n.budget-table .total-row {\r\n    background: #e2e8f0 !important;\r\n    font-weight: bold;\r\n}\r\n\r\n.status-badge {\r\n    padding: 8px 16px;\r\n    border-radius: 20px;\r\n    font-weight: bold;\r\n    font-size: 14px;\r\n}\r\n\r\n.activity-details-modal .modal-footer {\r\n    background: #f8f9fa;\r\n    padding: 20px 30px;\r\n    border-top: 1px solid #e2e8f0;\r\n    text-align: center;\r\n}\r\n\r\n.close-modal-btn {\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    color: #ffffff;\r\n    border: none;\r\n    padding: 12px 30px;\r\n    border-radius: 8px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    margin-left: 10px;\r\n}\r\n\r\n.close-modal-btn:hover {\r\n    background: linear-gradient(135deg, #16213e, #1a1a2e);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.export-docx-btn {\r\n    background: linear-gradient(135deg, #2563eb, #1d4ed8);\r\n    color: #ffffff;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.export-docx-btn:hover {\r\n    background: linear-gradient(135deg, #1d4ed8, #1e40af);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);\r\n}\r\n\r\n.export-docx-btn:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n.print-btn {\r\n    background: linear-gradient(135deg, #059669, #047857);\r\n    color: #ffffff;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 10px;\r\n}\r\n\r\n.print-btn:hover {\r\n    background: linear-gradient(135deg, #047857, #065f46);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);\r\n}\r\n\r\n.print-btn:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n/* Responsive styles for modal footer buttons */\r\n@media (max-width: 768px) {\r\n    .activity-details-modal .modal-footer {\r\n        flex-direction: column;\r\n        gap: 10px;\r\n    }\r\n\r\n    .print-btn,\r\n    .export-docx-btn,\r\n    .close-modal-btn {\r\n        width: 100%;\r\n        margin: 0;\r\n        justify-content: center;\r\n    }\r\n\r\n    .print-btn {\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n.activity-details-modal .modal-footer {\r\n    background: #f8f9fa;\r\n    padding: 20px 30px;\r\n    border-top: 1px solid #e2e8f0;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n/* Responsive Design for Activity Modal */\r\n@media (max-width: 768px) {\r\n    .activity-details-modal {\r\n        width: 98%;\r\n        max-height: 95vh;\r\n    }\r\n\r\n    .activity-document {\r\n        padding: 20px;\r\n    }\r\n\r\n    .logo-section {\r\n        flex-direction: column;\r\n        gap: 15px;\r\n    }\r\n\r\n    .organization-logo {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .logo-label {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .detail-row {\r\n        flex-direction: column;\r\n    }\r\n\r\n    .detail-label {\r\n        flex: none;\r\n        margin-bottom: 5px;\r\n        padding-right: 0;\r\n    }\r\n\r\n    .budget-table {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .budget-table th,\r\n    .budget-table td {\r\n        padding: 8px 4px;\r\n    }\r\n}\r\n</style>"], "mappings": ";;;OAutEkCA,UAA6B;OA6b9BC,UAA2B;OAQ3BC,UAA4B;;EAzcpDC,KAAK,EAAC;AAAQ;;EACVA,KAAK,EAAC;AAAgB;;EAMlBA,KAAK,EAAC;AAAa;;EAcnBA,KAAK,EAAC;AAAc;;EAOZA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAW;;;EAMNA,KAAK,EAAC;;;EAuBtCA,KAAK,EAAC;AAAc;;;EAEgBA,KAAK,EAAC;;;EAKlCA,KAAK,EAAC;AAAqB;;EAQ3BA,KAAK,EAAC;AAA2B;;;EAePA,KAAK,EAAC;;;EAChCA,KAAK,EAAC;AAAa;;EAKfA,KAAK,EAAC;AAAgB;;;;EAgB1BA,KAAK,EAAC;AAAsB;;;EACCA,KAAK,EAAC;;;;EAKOA,KAAK,EAAC;;;EAWpCA,KAAK,EAAC;AAAmB;;EAQzBA,KAAK,EAAC;AAAW;;EACbA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAKtBA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EActBA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAuB;;EAKjCA,KAAK,EAAC;AAAc;;EAmBIA,KAAK,EAAC;AAA4B;;EAClEA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAkB;;EACpBA,KAAK,EAAC;AAAY;;;;EAiBWC,CAAC,EAAC;;;;EACfA,CAAC,EAAC;;;EAKtBD,KAAK,EAAC;AAAa;;EAEVA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAMhCA,KAAK,EAAC;AAAe;;EACfA,KAAK,EAAC;AAAkB;;;EAcfA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAsB;;EAGnCA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAY;;EAIvBA,KAAK,EAAC;AAAU;;EACXA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EAG3BA,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAiB;;EACjBA,KAAK,EAAC;AAAkB;;EAE9BA,KAAK,EAAC;AAAiB;;EACjBA,KAAK,EAAC;AAAkB;;EAGzBA,KAAK,EAAC;AAAgB;;;;EAyBlDA,KAAK,EAAC;AAAc;;EAMhBA,KAAK,EAAC;AAAY;;EAYlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAYtBA,KAAK,EAAC;AAAc;;;;;;;;;EAcxBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;EAGjBA,KAAK,EAAC;AAAY;;EAYtBA,KAAK,EAAC;AAAc;;;EAYpBA,KAAK,EAAC;AAAc;;EAKpBA,KAAK,EAAC;AAA8B;;EAEhCA,KAAK,EAAC;AAAiB;;EAenBA,KAAK,EAAC;AAAgB;;EAM1BA,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAc;;EAGxBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAc;;;EAGxBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;;EAGxBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAY;;;EAMzBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAoB;;;EAMjCA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;EAGxBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAc;;;EAGxBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;;EAGxBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;;EAGxBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;;EAGxBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;;EAGxBA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;EACdA,KAAK,EAAC;AAAc;;;;;EAoBfA,KAAK,EAAC;AAAW;;;EAShCA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAa;;EAM1BA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAc;;EAOxBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAc;;EAGxBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAc;;EAGxBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAc;;EAKhCA,KAAK,EAAC;AAAc;;EAEZE,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACC,KAAyB,EAAzB;IAAA;EAAA;;;EAM/DJ,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,cAAc;EAACC,KAAyB,EAAzB;IAAA;EAAA;;;6DA9lBpFC,mBAAA,CAyDM,OAzDNC,UAyDM,GAxDFD,mBAAA,CAuDM,OAvDNE,UAuDM,G,4BAtDFF,mBAAA,CAIM;IAJDP,KAAK,EAAC;EAAa,IACpBO,mBAAA,CAAkD;IAA5CP,KAAK,EAAC;EAAU,GAAC,sBAAoB,GAC3CO,mBAAA,CAAkE;IAA7DP,KAAK,EAAC,MAAM;IAACU,GAA6B,EAA7Bb,UAA6B;IAACc,GAAG,EAAC;MACpDJ,mBAAA,CAA0D;IAApDP,KAAK,EAAC;EAAW,GAAC,6BAA2B,E,qBAEvDO,mBAAA,CAaM,OAbNK,UAaM,GAZFL,mBAAA,CAKS;IALDP,KAAK,EAAAa,eAAA,EAAC,SAAS;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;IAAA;IAAkBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;uCACxFd,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAA+C;IAAzCN,CAAC,EAAC;EAAqC,G,oBAEjDM,mBAAA,CAA2B,cAArB,gBAAc,mB,qBAExBA,mBAAA,CAKS;IALDP,KAAK,EAAAa,eAAA,EAAC,SAAS;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;IAAA;IAAgBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,cAAc;uCACtFd,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAAwG;IAAlGN,CAAC,EAAC;EAA8F,G,oBAE1GM,mBAAA,CAA6B,cAAvB,kBAAgB,mB,uBAG9BA,mBAAA,CAkCM,OAlCNe,UAkCM,GAjCFf,mBAAA,CAYM;IAZDP,KAAK,EAAC,aAAa;IAAEiB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEH,QAAA,CAAAI,cAAA,IAAAJ,QAAA,CAAAI,cAAA,IAAAD,IAAA,CAAc;kCAC3ChB,mBAAA,CAIM;IAJDP,KAAK,EAAC;EAAa,IACpBO,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAAyH;IAAnHN,CAAC,EAAC;EAA+G,G,uBAG/HM,mBAAA,CAKM,OALNkB,UAKM,GAJFlB,mBAAA,CAAoF,QAApFmB,UAAoF,EAAAC,gBAAA,CAAzDZ,KAAA,CAAAa,IAAI,EAAEC,SAAS,IAAId,KAAA,CAAAa,IAAI,EAAEE,QAAQ,gC,4BAC5DvB,mBAAA,CAEM;IAFDP,KAAK,EAAC,gBAAgB;IAACE,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACxEE,mBAAA,CAA0B;IAApBN,CAAC,EAAC;EAAgB,G,yBAIzBc,KAAA,CAAAgB,YAAY,I,cAAvBC,mBAAA,CAmBM,OAnBNC,UAmBM,GAlBF1B,mBAAA,CAKM;IALDP,KAAK,EAAC,gBAAgB;IAAEiB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEH,QAAA,CAAAc,mBAAA,IAAAd,QAAA,CAAAc,mBAAA,IAAAX,IAAA,CAAmB;uCACnDhB,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAAyrB;IAAnrBN,CAAC,EAAC;EAA+qB,G,oBAE3rBM,mBAAA,CAA2B,cAArB,gBAAc,mB,MAEbQ,KAAA,CAAAa,IAAI,IAAIb,KAAA,CAAAa,IAAI,CAACO,IAAI,gB,cAA5BH,mBAAA,CAKM;;IALoChC,KAAK,EAAC,gBAAgB;IAAEiB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEH,QAAA,CAAAgB,SAAA,IAAAhB,QAAA,CAAAgB,SAAA,IAAAb,IAAA,CAAS;uCAC9EhB,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAAgM;IAA1LN,CAAC,EAAC;EAAsL,G,oBAElMM,mBAAA,CAAyB,cAAnB,cAAY,mB,2CAEtBA,mBAAA,CAKM;IALDP,KAAK,EAAC,gBAAgB;IAAEiB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEH,QAAA,CAAAiB,MAAA,IAAAjB,QAAA,CAAAiB,MAAA,IAAAd,IAAA,CAAM;uCACtChB,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAA0H;IAApHN,CAAC,EAAC;EAAgH,G,oBAE5HM,mBAAA,CAAyB,cAAnB,cAAY,mB,mDAMtCA,mBAAA,CAwQM,OAxQN+B,UAwQM,GAvQFC,mBAAA,4BAA+B,EACpBxB,KAAA,CAAAC,WAAW,iB,cAAtBgB,mBAAA,CAyBM,OAzBNQ,UAyBM,G,4BAxBFjC,mBAAA,CAGM;IAHDP,KAAK,EAAC;EAAa,IACpBO,mBAAA,CAAkD;IAA9CP,KAAK,EAAC;EAAY,GAAC,wBAAsB,GAC7CO,mBAAA,CAAuF;IAApFP,KAAK,EAAC;EAAkB,GAAC,yDAAuD,E,qBAEvFO,mBAAA,CAIM,OAJNkC,WAIM,G,4BAHFlC,mBAAA,CAAuD;IAAjDP,KAAK,EAAC;EAAiB,GAAC,oBAAkB,qB,4BAChDO,mBAAA,CAAuE;IAAhEmC,GAAG,EAAC,kBAAkB;IAAC1C,KAAK,EAAC;KAAc,eAAa,qB,gBAC/DO,mBAAA,CAA6I;IAAtIoC,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,kBAAkB;IAACC,WAAW,EAAC,mBAAmB;IAAC7C,KAAK,EAAC,iBAAiB;+DAAUe,KAAA,CAAA+B,eAAe,GAAA3B,MAAA;IAAE4B,QAAQ,EAAR,EAAQ;IAACC,QAAQ,EAAR;iDAA1BjC,KAAA,CAAA+B,eAAe,E,iCAG7HvC,mBAAA,CAA4B;IAAvBP,KAAK,EAAC;EAAU,4BAErBO,mBAAA,CAOM,OAPN0C,WAOM,G,4BANF1C,mBAAA,CAAuD;IAAjDP,KAAK,EAAC;EAAiB,GAAC,oBAAkB,qB,4BAChDO,mBAAA,CACM;IADDP,KAAK,EAAC;EAAiB,4BAE5BO,mBAAA,CAES;IAFDD,KAAuC,EAAvC;MAAA;MAAA;IAAA,CAAuC;IAAEW,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEH,QAAA,CAAA8B,eAAA,IAAA9B,QAAA,CAAA8B,eAAA,IAAA3B,IAAA,CAAe;uCACnEhB,mBAAA,CAA4B,cAAtB,iBAAe,mB,oCAG7BA,mBAAA,CAA4B;IAAvBP,KAAK,EAAC;EAAU,4BACrBO,mBAAA,CAES;IAFDD,KAAmE,EAAnE;MAAA;MAAA;MAAA;IAAA,CAAmE;IAAEW,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEH,QAAA,CAAA+B,QAAA,IAAA/B,QAAA,CAAA+B,QAAA,IAAA5B,IAAA,CAAQ;uCACxFhB,mBAAA,CAA2B,cAArB,gBAAc,mB,6CAI5BgC,mBAAA,6BAAgC,EACrBxB,KAAA,CAAAC,WAAW,e,cAAtBgB,mBAAA,CAyOM,OAzONoB,WAyOM,GAxOF7C,mBAAA,CAmBM,OAnBN8C,WAmBM,G,4BAlBF9C,mBAAA,CAGM;IAHDP,KAAK,EAAC;EAAqB,IAC5BO,mBAAA,CAAoC;IAAhCP,KAAK,EAAC;EAAY,GAAC,UAAQ,GAC/BO,mBAAA,CAAyI;IAAtIP,KAAK,EAAC;EAAkB,GAAC,2GAAyG,E,qBAEzIO,mBAAA,CAaM,OAbN+C,WAaM,GAZF/C,mBAAA,CAKS;IALAU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEH,QAAA,CAAAmC,WAAA,IAAAnC,QAAA,CAAAmC,WAAA,IAAAhC,IAAA,CAAW;IAAEvB,KAAK,EAAC,YAAY;IAAE+C,QAAQ,EAAEhC,KAAA,CAAAyC,iBAAiB,IAAIpC,QAAA,CAAAqC,kBAAkB,CAACC,MAAM;IAAQC,KAAK,EAAC;uCACnHpD,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAAmG;IAA7FN,CAAC,EAAC;EAAyF,G,oBAErGM,mBAAA,CAAsB,cAAhB,WAAS,mB,kCAEnBA,mBAAA,CAKS;IALAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAAwC,iBAAA,IAAAxC,QAAA,CAAAwC,iBAAA,IAAArC,IAAA,CAAiB;IAAEvB,KAAK,EAAC,aAAa;IAAE+C,QAAQ,EAAEhC,KAAA,CAAAyC;qBAC9DxB,mBAAA,CAEM;IAFD9B,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,cAAc;IAAEL,KAAK,EAAAa,eAAA;MAAA,YAAgBE,KAAA,CAAAyC;IAAiB;uCACvGjD,mBAAA,CAAsN;IAAhNN,CAAC,EAAC;EAA4M,0B,kDAExNM,mBAAA,CAAkB,cAAZ,OAAK,oB,mCAKvBA,mBAAA,CAkNM,OAlNNsD,WAkNM,GAjNS9C,KAAA,CAAAyC,iBAAiB,I,cAA5BxB,mBAAA,CAGM,OAHN8B,WAGM,OAAA5C,MAAA,SAAAA,MAAA,QAFFX,mBAAA,CAAmC;IAA9BP,KAAK,EAAC;EAAiB,2BAC5BO,mBAAA,CAAmC,cAA7B,wBAAsB,mB,QAGhBQ,KAAA,CAAAgD,YAAY,CAACL,MAAM,U,cAAnC1B,mBAAA,CAMM,OANNgC,WAMM,OAAA9C,MAAA,SAAAA,MAAA,QALFX,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,cAAc;IAACL,KAAK,EAAC;MACtEO,mBAAA,CAAiI;IAA3HN,CAAC,EAAC;EAAuH,G,oBAEnIM,mBAAA,CAAuB,YAAnB,gBAAc,oBAClBA,mBAAA,CAA+C,WAA5C,0CAAwC,mB,2CAG/CgC,mBAAA,qBAAwB,EACbxB,KAAA,CAAAkD,eAAe,I,cAA1BjC,mBAAA,CA8DM;;IA9DsBhC,KAAK,EAAC,oBAAoB;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA8C,UAAA,IAAA9C,QAAA,CAAA8C,UAAA,IAAA3C,IAAA,CAAU;MACrEhB,mBAAA,CA4DM;IA5DDP,KAAK,EAAC,YAAY;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAiD,cAAA,CAAN,QAAW;MAC/B5D,mBAAA,CAOM,OAPN6D,WAOM,G,4BANF7D,mBAAA,CAAqB,YAAjB,cAAY,qBAChBA,mBAAA,CAIS;IAJAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA8C,UAAA,IAAA9C,QAAA,CAAA8C,UAAA,IAAA3C,IAAA,CAAU;IAAEvB,KAAK,EAAC;uCAC9BO,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAAiH;IAA3GN,CAAC,EAAC;EAAuG,G,2BAI3HM,mBAAA,CAkDM,OAlDN8D,WAkDM,GAjDF9D,mBAAA,CASM,OATN+D,WASM,GARF/D,mBAAA,CAGM,OAHNgE,WAGM,G,4BAFFhE,mBAAA,CAA+B,eAAxB,kBAAgB,qB,gBACvBA,mBAAA,CAA2E;iEAA3DQ,KAAA,CAAAkD,eAAe,CAACO,UAAU,GAAArD,MAAA;IAAEwB,IAAI,EAAC,MAAM;IAAC3C,KAAK,EAAC;iDAA9Ce,KAAA,CAAAkD,eAAe,CAACO,UAAU,E,KAE9CjE,mBAAA,CAGM,OAHNkE,WAGM,G,4BAFFlE,mBAAA,CAA4B,eAArB,eAAa,qB,gBACpBA,mBAAA,CAAsE;iEAAtDQ,KAAA,CAAAkD,eAAe,CAACN,KAAK,GAAAxC,MAAA;IAAEwB,IAAI,EAAC,MAAM;IAAC3C,KAAK,EAAC;iDAAzCe,KAAA,CAAAkD,eAAe,CAACN,KAAK,E,OAG7CpD,mBAAA,CAkBM,OAlBNmE,WAkBM,GAjBFnE,mBAAA,CAGM,OAHNoE,WAGM,G,4BAFFpE,mBAAA,CAA4B,eAArB,eAAa,qB,gBACpBA,mBAAA,CAA8E;iEAA9DQ,KAAA,CAAAkD,eAAe,CAACW,aAAa,GAAAzD,MAAA;IAAEwB,IAAI,EAAC,MAAM;IAAC3C,KAAK,EAAC;iDAAjDe,KAAA,CAAAkD,eAAe,CAACW,aAAa,E,KAEjDrE,mBAAA,CAYM,OAZNsE,WAYM,G,4BAXFtE,mBAAA,CAA2B,eAApB,cAAY,qB,gBACnBA,mBAAA,CASS;iEATQQ,KAAA,CAAAkD,eAAe,CAACa,KAAK,GAAA3D,MAAA;IAAEnB,KAAK,EAAC;geAA7Be,KAAA,CAAAkD,eAAe,CAACa,KAAK,E,OAY9CvE,mBAAA,CAKM,OALNwE,WAKM,GAJFxE,mBAAA,CAGM,OAHNyE,WAGM,G,4BAFFzE,mBAAA,CAAyB,eAAlB,YAAU,qB,gBACjBA,mBAAA,CAAgG;iEAA7EQ,KAAA,CAAAkD,eAAe,CAACgB,iBAAiB,GAAA9D,MAAA;IAAEnB,KAAK,EAAC,eAAe;IAACkF,IAAI,EAAC;iDAA9DnE,KAAA,CAAAkD,eAAe,CAACgB,iBAAiB,E,OAG5D1E,mBAAA,CAaM,OAbN4E,WAaM,GAZF5E,mBAAA,CAKS;IALAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAAgE,YAAA,IAAAhE,QAAA,CAAAgE,YAAA,IAAA7D,IAAA,CAAY;IAAEvB,KAAK,EAAC;uCAChCO,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAA6D;IAAvDN,CAAC,EAAC;EAAmD,G,qCACzD,iBAEV,mB,MACAM,mBAAA,CAKS;IALAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA8C,UAAA,IAAA9C,QAAA,CAAA8C,UAAA,IAAA3C,IAAA,CAAU;IAAEvB,KAAK,EAAC;uCAC9BO,mBAAA,CAEM;IAFDL,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MACjDE,mBAAA,CAAiH;IAA3GN,CAAC,EAAC;EAAuG,G,qCAC7G,SAEV,mB,gBAOAc,KAAA,CAAAgD,YAAY,CAACL,MAAM,Q,cAAnC1B,mBAAA,CAiIMqD,SAAA;IAAAC,GAAA;EAAA,IAlIN/C,mBAAA,sBAAyB,EACzBhC,mBAAA,CAiIM,OAjINgF,WAiIM,GAhIFhF,mBAAA,CAgEM,OAhENiF,WAgEM,GA/DFjD,mBAAA,gBAAmB,EACnBhC,mBAAA,CAsBM,OAtBNkF,WAsBM,GArBFlF,mBAAA,CAoBM,OApBNmF,WAoBM,G,gBAnBFnF,mBAAA,CAKE;IAJEoC,IAAI,EAAC,MAAM;iEACF5B,KAAA,CAAA4E,WAAW,GAAAxE,MAAA;IACpB0B,WAAW,EAAC,2CAA2C;IACvD7C,KAAK,EAAC;iDAFGe,KAAA,CAAA4E,WAAW,E,kBAIxB3D,mBAAA,CAYM;IAXFhC,KAAK,EAAC,aAAa;IACnBE,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,OAAO,EAAC,WAAW;IACnBC,IAAI,EAAC,cAAc;IAClBY,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAAwE,qBAAA,IAAAxE,QAAA,CAAAwE,qBAAA,IAAArE,IAAA,CAAqB;IAC5BoC,KAAK,EAAE5C,KAAA,CAAA4E,WAAW;IAClB,YAAU,EAAE5E,KAAA,CAAA4E,WAAW;OAEX5E,KAAA,CAAA4E,WAAW,I,cAAxB3D,mBAAA,CAA0Q,QAA1Q6D,WAA0Q,M,cAC1Q7D,mBAAA,CAAwH,QAAxH8D,WAAwH,G,oCAKpIvF,mBAAA,CAqCM,OArCNwF,WAqCM,GApCFxF,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAchF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAChG3F,mBAAA,CAA0D,QAA1D4F,WAA0D,EAAAxE,gBAAA,CAA7BZ,KAAA,CAAAgD,YAAY,CAACL,MAAM,kB,4BAChDnD,mBAAA,CAA+C;IAAzCP,KAAK,EAAC;EAAY,GAAC,iBAAe,oB,kBAE5CO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAqBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACvG3F,mBAAA,CAA+F,QAA/F6F,WAA+F,EAAAzE,gBAAA,CAAlEZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,kBAAkBpB,MAAM,kB,4BACrFnD,mBAAA,CAAyC;IAAnCP,KAAK,EAAC;EAAY,GAAC,WAAS,oB,kBAEtCO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAyBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAC3G3F,mBAAA,CAAmG,QAAnGgG,WAAmG,EAAA5E,gBAAA,CAAtEZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,sBAAsBpB,MAAM,kB,4BACzFnD,mBAAA,CAA6C;IAAvCP,KAAK,EAAC;EAAY,GAAC,eAAa,oB,kBAE1CO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAiBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACnG3F,mBAAA,CAA2F,QAA3FiG,WAA2F,EAAA7E,gBAAA,CAA9DZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,cAAcpB,MAAM,kB,4BACjFnD,mBAAA,CAAqC;IAA/BP,KAAK,EAAC;EAAY,GAAC,OAAK,oB,kBAElCO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAiBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACnG3F,mBAAA,CAA2F,QAA3FkG,WAA2F,EAAA9E,gBAAA,CAA9DZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,cAAcpB,MAAM,kB,4BACjFnD,mBAAA,CAAqC;IAA/BP,KAAK,EAAC;EAAY,GAAC,OAAK,oB,kBAElCO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAuBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MACzG3F,mBAAA,CAAiG,QAAjGmG,WAAiG,EAAA/E,gBAAA,CAApEZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,oBAAoBpB,MAAM,kB,4BACvFnD,mBAAA,CAA2C;IAArCP,KAAK,EAAC;EAAY,GAAC,aAAW,oB,kBAExCO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAyBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAC3G3F,mBAAA,CAAmG,QAAnGoG,WAAmG,EAAAhF,gBAAA,CAAtEZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,sBAAsBpB,MAAM,kB,4BACzFnD,mBAAA,CAA6C;IAAvCP,KAAK,EAAC;EAAY,GAAC,eAAa,oB,kBAE1CO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAyBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAC3G3F,mBAAA,CAAmG,QAAnGqG,WAAmG,EAAAjF,gBAAA,CAAtEZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,sBAAsBpB,MAAM,kB,4BACzFnD,mBAAA,CAA6C;IAAvCP,KAAK,EAAC;EAAY,GAAC,eAAa,oB,kBAE1CO,mBAAA,CAGM;IAHDP,KAAK,EAAAa,eAAA,EAAC,qBAAqB;MAAAmF,QAAA,EAAqBjF,KAAA,CAAAkF,cAAc;IAAA;IAAgBhF,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAA8E,YAAY;MAClG3F,mBAAA,CAA0F,QAA1FsG,WAA0F,EAAAlF,gBAAA,CAA7DZ,KAAA,CAAAgD,YAAY,CAACsC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,aAAapB,MAAM,kB,4BAChFnD,mBAAA,CAAoC;IAA9BP,KAAK,EAAC;EAAY,GAAC,MAAI,oB,sBAKzCO,mBAAA,CA6DM,OA7DNuG,WA6DM,GA5DFvG,mBAAA,CA2DQ,SA3DRwG,WA2DQ,G,8BA1DJxG,mBAAA,CAUQ,gBATJA,mBAAA,CAQK,aAPDA,mBAAA,CAAuC;IAAnCP,KAAK,EAAC;EAAW,GAAC,cAAY,GAClCO,mBAAA,CAAsC;IAAlCP,KAAK,EAAC;EAAW,GAAC,aAAW,GACjCO,mBAAA,CAAsC;IAAlCP,KAAK,EAAC;EAAU,GAAC,cAAY,GACjCO,mBAAA,CAAkC;IAA9BP,KAAK,EAAC;EAAY,GAAC,QAAM,GAC7BO,mBAAA,CAAyC;IAArCP,KAAK,EAAC;EAAiB,GAAC,UAAQ,GACpCO,mBAAA,CAAuC;IAAnCP,KAAK,EAAC;EAAiB,GAAC,QAAM,GAClCO,mBAAA,CAAsC;IAAlCP,KAAK,EAAC;EAAa,GAAC,WAAS,E,uBAGzCO,mBAAA,CA8CQ,iB,kBA7CJyB,mBAAA,CA4CKqD,SAAA,QAAA2B,WAAA,CA5CkB5F,QAAA,CAAAqC,kBAAkB,EAA9BwD,QAAQ;yBAAnBjF,mBAAA,CA4CK;MA5CuCsD,GAAG,EAAE2B,QAAQ,CAACrE,EAAE;MAAE5C,KAAK,EAAC,4BAA4B;MAAEiB,OAAK,EAAAE,MAAA,IAAEC,QAAA,CAAA8F,iBAAiB,CAACD,QAAQ;QAC/H1G,mBAAA,CAKK,MALL4G,WAKK,GAJD5G,mBAAA,CAGM,OAHN6G,WAGM,GAFF7G,mBAAA,CAA6B,YAAAoB,gBAAA,CAAtBsF,QAAQ,CAACtD,KAAK,kBACrBpD,mBAAA,CAAqF,KAArF8G,WAAqF,EAAA1F,gBAAA,CAAlDsF,QAAQ,CAAChC,iBAAiB,kC,KAGrE1E,mBAAA,CAIK,MAJL+G,WAIK,GAHD/G,mBAAA,CAEM,OAFNgH,WAEM,GADFhH,mBAAA,CAAsC,cAAAoB,gBAAA,CAA7BsF,QAAQ,CAACzC,UAAU,iB,KAGpCjE,mBAAA,CAKK,MALLiH,WAKK,GAJDjH,mBAAA,CAGM,OAHNkH,WAGM,GAFFlH,mBAAA,CAAqI,QAArImH,WAAqI,EAAA/F,gBAAA,KAAtGgG,IAAI,CAACV,QAAQ,CAACrC,aAAa,EAAEgD,kBAAkB;MAAAC,GAAA;MAAAC,KAAA;IAAA,oBAC9EvH,mBAAA,CAAmF,QAAnFwH,WAAmF,EAAApG,gBAAA,KAApDgG,IAAI,CAACV,QAAQ,CAACrC,aAAa,EAAEoD,WAAW,mB,KAG/EzH,mBAAA,CAKK,MALL0H,WAKK,GAJD1H,mBAAA,CAGO;MAHDP,KAAK,EAAAa,eAAA,EAAC,cAAc,EAASO,QAAA,CAAA8G,cAAc,CAACjB,QAAQ,CAACnC,KAAK;oCAC5DvE,mBAAA,CAAoC;MAA/BP,KAAK,EAAC;IAAkB,4B,iBAAO,GACpC,GAAA2B,gBAAA,CAAGsF,QAAQ,CAACnC,KAAK,iB,oBAGzBvE,mBAAA,CAEK,MAFL4H,WAEK,GADD5H,mBAAA,CAAgF,QAAhF6H,WAAgF,EAAAzG,gBAAA,CAA9CsF,QAAQ,CAACoB,eAAe,CAACC,WAAW,iB,GAE1E/H,mBAAA,CAEK,MAFLgI,WAEK,GADDhI,mBAAA,CAAqF,QAArFiI,WAAqF,EAAA7G,gBAAA,CAAnDsF,QAAQ,CAACoB,eAAe,CAACI,gBAAgB,iB,GAE/ElI,mBAAA,CAaK;MAbDP,KAAK,EAAC,aAAa;MAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAiD,cAAA,CAAN,QAAW;QAC/B5D,mBAAA,CAWM,OAXNmI,WAWM,GAVFnI,mBAAA,CAIS;MAJAU,OAAK,EAAAE,MAAA,IAAEC,QAAA,CAAAuH,YAAY,CAAC1B,QAAQ;MAAGjH,KAAK,EAAC,qBAAqB;MAAC2D,KAAK,EAAC;yCACtEpD,mBAAA,CAEM;MAFDL,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QACjDE,mBAAA,CAAiK;MAA3JN,CAAC,EAAC;IAAuJ,G,qDAGvKM,mBAAA,CAIS;MAJAU,OAAK,EAAAE,MAAA,IAAEC,QAAA,CAAAwH,cAAc,CAAC3B,QAAQ,CAACrE,EAAE;MAAG5C,KAAK,EAAC,uBAAuB;MAAC2D,KAAK,EAAC;2CAC7EpD,mBAAA,CAEM;MAFDL,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QACjDE,mBAAA,CAAyF;MAAnFN,CAAC,EAAC;IAA+E,G;2KAcvIsC,mBAAA,4BAA+B,EACpBxB,KAAA,CAAA8H,mBAAmB,I,cAA9B7G,mBAAA,CA6EM;;IA7E0BhC,KAAK,EAAC,eAAe;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA0H,oBAAA,IAAA1H,QAAA,CAAA0H,oBAAA,IAAAvH,IAAA,CAAoB;MAC9EhB,mBAAA,CA2EM;IA3EDP,KAAK,EAAC,sCAAsC;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAiD,cAAA,CAAN,QAAW;MACzD5D,mBAAA,CAGM,OAHNwI,WAGM,G,8BAFFxI,mBAAA,CAAuB,YAAnB,gBAAc,qBAClBA,mBAAA,CAAwE;IAA/DU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA0H,oBAAA,IAAA1H,QAAA,CAAA0H,oBAAA,IAAAvH,IAAA,CAAoB;IAAEvB,KAAK,EAAC;KAAY,GAAO,E,GAGnEO,mBAAA,CAoEO;IApEAyI,QAAM,EAAA9H,MAAA,SAAAA,MAAA,OAAAiD,cAAA,KAAA5C,IAAA,KAAUH,QAAA,CAAA6H,qBAAA,IAAA7H,QAAA,CAAA6H,qBAAA,IAAA1H,IAAA,CAAqB;IAAEvB,KAAK,EAAC;MAChDO,mBAAA,CAUM,OAVN2I,WAUM,G,8BATF3I,mBAAA,CAA2C;IAApCmC,GAAG,EAAC;EAAU,GAAC,eAAa,qB,gBACnCnC,mBAAA,CAOC;IANGoC,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,UAAU;iEACJ7B,KAAA,CAAAoI,WAAW,CAACC,QAAQ,GAAAjI,MAAA;IAC7B6B,QAAQ,EAAR,EAAQ;IACRhD,KAAK,EAAC,YAAY;IAClB6C,WAAW,EAAC;iDAHH9B,KAAA,CAAAoI,WAAW,CAACC,QAAQ,E,KAOrC7I,mBAAA,CASM,OATN8I,WASM,G,8BARF9I,mBAAA,CAA+C;IAAxCmC,GAAG,EAAC;EAAS,GAAC,oBAAkB,qB,gBACvCnC,mBAAA,CAMC;IALGoC,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,SAAS;iEACH7B,KAAA,CAAAoI,WAAW,CAACG,OAAO,GAAAnI,MAAA;IAC5BnB,KAAK,EAAC,YAAY;IAClB6C,WAAW,EAAC;iDAFH9B,KAAA,CAAAoI,WAAW,CAACG,OAAO,E,KAMpC/I,mBAAA,CAmCM,OAnCNgJ,WAmCM,G,8BAlCFhJ,mBAAA,CAAoC,YAAhC,6BAA2B,qBAE/BA,mBAAA,CASM,OATNiJ,WASM,G,8BARFjJ,mBAAA,CAAyD;IAAlDmC,GAAG,EAAC;EAAiB,GAAC,sBAAoB,qB,gBACjDnC,mBAAA,CAMC;IALGoC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,iBAAiB;iEACX7B,KAAA,CAAAoI,WAAW,CAACM,eAAe,GAAAtI,MAAA;IACpCnB,KAAK,EAAC,YAAY;IAClB6C,WAAW,EAAC;iDAFH9B,KAAA,CAAAoI,WAAW,CAACM,eAAe,E,KAM5ClJ,mBAAA,CASM,OATNmJ,WASM,G,8BARFnJ,mBAAA,CAAqD;IAA9CmC,GAAG,EAAC;EAAa,GAAC,sBAAoB,qB,gBAC7CnC,mBAAA,CAMC;IALGoC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,aAAa;iEACP7B,KAAA,CAAAoI,WAAW,CAACQ,WAAW,GAAAxI,MAAA;IAChCnB,KAAK,EAAC,YAAY;IAClB6C,WAAW,EAAC;iDAFH9B,KAAA,CAAAoI,WAAW,CAACQ,WAAW,E,KAMxCpJ,mBAAA,CASM,OATNqJ,WASM,G,8BARFrJ,mBAAA,CAA+D;IAAxDmC,GAAG,EAAC;EAAiB,GAAC,4BAA0B,qB,gBACvDnC,mBAAA,CAMC;IALGoC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,iBAAiB;iEACX7B,KAAA,CAAAoI,WAAW,CAACU,eAAe,GAAA1I,MAAA;IACpCnB,KAAK,EAAC,YAAY;IAClB6C,WAAW,EAAC;iDAFH9B,KAAA,CAAAoI,WAAW,CAACU,eAAe,E,OAOhDtJ,mBAAA,CAMM,OANNuJ,WAMM,GALFvJ,mBAAA,CAAqF;IAA7EoC,IAAI,EAAC,QAAQ;IAAE1B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA0H,oBAAA,IAAA1H,QAAA,CAAA0H,oBAAA,IAAAvH,IAAA,CAAoB;IAAEvB,KAAK,EAAC;KAAa,OAAK,GAC5EO,mBAAA,CAGS;IAHDoC,IAAI,EAAC,QAAQ;IAAEI,QAAQ,EAAEhC,KAAA,CAAAgJ,eAAe;IAAE/J,KAAK,EAAC;MACxCe,KAAA,CAAAgJ,eAAe,I,cAA3B/H,mBAAA,CAAiD,QAAAgI,WAAA,EAApB,eAAa,M,cAC1ChI,mBAAA,CAAiC,QAAAiI,WAAA,EAApB,eAAa,G,qGAO9C1H,mBAAA,4BAA+B,EACpBxB,KAAA,CAAAmJ,mBAAmB,I,cAA9BlI,mBAAA,CA6BM;;IA7B0BhC,KAAK,EAAC,eAAe;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA+I,oBAAA,IAAA/I,QAAA,CAAA+I,oBAAA,IAAA5I,IAAA,CAAoB;MAC9EhB,mBAAA,CA2BM;IA3BDP,KAAK,EAAC,yBAAyB;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAiD,cAAA,CAAN,QAAW;MAC5C5D,mBAAA,CAGM,OAHN6J,WAGM,G,8BAFF7J,mBAAA,CAAsB,YAAlB,eAAa,qBACjBA,mBAAA,CAAwE;IAA/DU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA+I,oBAAA,IAAA/I,QAAA,CAAA+I,oBAAA,IAAA5I,IAAA,CAAoB;IAAEvB,KAAK,EAAC;KAAY,GAAO,E,GAEnEO,mBAAA,CAeM,OAfN8J,WAeM,GAdF9J,mBAAA,CAEI,KAFJ+J,WAEI,EAAA3I,gBAAA,CADGZ,KAAA,CAAAwJ,mBAAmB,CAACC,MAAM,uIAEjCjK,mBAAA,CAUM,OAVNkK,WAUM,G,8BATFlK,mBAAA,CAAkD;IAA3CmC,GAAG,EAAC;EAAY,GAAC,oBAAkB,qB,gBAC1CnC,mBAAA,CAOC;IANGoC,IAAI,EAAC,UAAU;IACfC,EAAE,EAAC,YAAY;iEACN7B,KAAA,CAAAwJ,mBAAmB,CAACG,GAAG,GAAAvJ,MAAA;IAChC0B,WAAW,EAAC,wBAAwB;IACnC8H,OAAK,EAAAzJ,MAAA,SAAAA,MAAA,OAAA0J,SAAA,KAAArJ,IAAA,KAAQH,QAAA,CAAAyJ,gBAAA,IAAAzJ,QAAA,CAAAyJ,gBAAA,IAAAtJ,IAAA,CAAgB;IAC9BvB,KAAK,EAAC;iEAHGe,KAAA,CAAAwJ,mBAAmB,CAACG,GAAG,E,OAO5CnK,mBAAA,CAKM,OALNuK,WAKM,GAJFvK,mBAAA,CAAuE;IAA9DU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA+I,oBAAA,IAAA/I,QAAA,CAAA+I,oBAAA,IAAA5I,IAAA,CAAoB;IAAEvB,KAAK,EAAC;KAAa,OAAK,GAC9DO,mBAAA,CAES;IAFAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAAyJ,gBAAA,IAAAzJ,QAAA,CAAAyJ,gBAAA,IAAAtJ,IAAA,CAAgB;IAAEvB,KAAK,EAAC,aAAa;IAAE+C,QAAQ,GAAGhC,KAAA,CAAAwJ,mBAAmB,CAACG;sBAC/E3J,KAAA,CAAAwJ,mBAAmB,CAACC,MAAM,uDAAAO,WAAA,E,4CAM7CxI,mBAAA,4BAA+B,EACpBxB,KAAA,CAAAiK,iBAAiB,IAAIjK,KAAA,CAAAkK,gBAAgB,I,cAAhDjJ,mBAAA,CAmLM;;IAnL4ChC,KAAK,EAAC,eAAe;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA8J,kBAAA,IAAA9J,QAAA,CAAA8J,kBAAA,IAAA3J,IAAA,CAAkB;MAC9FhB,mBAAA,CAiLM;IAjLDP,KAAK,EAAC,sCAAsC;IAAEiB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAiD,cAAA,CAAN,QAAW;MACzD5D,mBAAA,CAGM,OAHN4K,WAGM,G,8BAFF5K,mBAAA,CAAsB,YAAlB,eAAa,qBACjBA,mBAAA,CAAsE;IAA7DU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA8J,kBAAA,IAAA9J,QAAA,CAAA8J,kBAAA,IAAA3J,IAAA,CAAkB;IAAEvB,KAAK,EAAC;KAAY,GAAO,E,GAGjEO,mBAAA,CAwJM,OAxJN6K,WAwJM,GAvJF7I,mBAAA,qBAAwB,EACxBhC,mBAAA,CAkBM,OAlBN8K,WAkBM,G,yiBAHF9K,mBAAA,CAEM,OAFN+K,WAEM,GADF/K,mBAAA,CAAqC,YAAAoB,gBAAA,CAA9BZ,KAAA,CAAAkK,gBAAgB,CAACtH,KAAK,iB,KAIrCpB,mBAAA,4BAA+B,EAC/BhC,mBAAA,CAgIM,OAhINgL,WAgIM,GA/HFhL,mBAAA,CAGM,OAHNiL,WAGM,G,8BAFFjL,mBAAA,CAAqC;IAAhCP,KAAK,EAAC;EAAc,GAAC,OAAK,qBAC/BO,mBAAA,CAAoI,OAApIkL,WAAoI,EAA1G,gCAA8B,GAAA9J,gBAAA,CAAGZ,KAAA,CAAAkK,gBAAgB,CAAC3C,WAAW,IAAG,KAAG,GAAA3G,gBAAA,CAAGZ,KAAA,CAAAkK,gBAAgB,CAACzG,UAAU,iB,GAG/HjE,mBAAA,CAGM,OAHNmL,WAGM,G,8BAFFnL,mBAAA,CAA0C;IAArCP,KAAK,EAAC;EAAc,GAAC,YAAU,qBACpCO,mBAAA,CAA4D,OAA5DoL,WAA4D,EAAAhK,gBAAA,CAA/BZ,KAAA,CAAAkK,gBAAgB,CAACtH,KAAK,iB,GAGzB5C,KAAA,CAAAkK,gBAAgB,CAACW,aAAa,I,cAA5D5J,mBAAA,CAGM,OAHN6J,WAGM,G,8BAFFtL,mBAAA,CAA2C;IAAtCP,KAAK,EAAC;EAAc,GAAC,aAAW,qBACrCO,mBAAA,CAAoE,OAApEuL,WAAoE,EAAAnK,gBAAA,CAAvCZ,KAAA,CAAAkK,gBAAgB,CAACW,aAAa,iB,wCAGjC7K,KAAA,CAAAkK,gBAAgB,CAACc,cAAc,IAAIhL,KAAA,CAAAkK,gBAAgB,CAACc,cAAc,CAACrI,MAAM,Q,cAAvG1B,mBAAA,CAOM,OAPNgK,WAOM,G,8BANFzL,mBAAA,CAA4C;IAAvCP,KAAK,EAAC;EAAc,GAAC,cAAY,qBACtCO,mBAAA,CAIM,OAJN0L,WAIM,GAHF1L,mBAAA,CAEK,MAFL2L,WAEK,I,kBADDlK,mBAAA,CAA2FqD,SAAA,QAAA2B,WAAA,CAAxEjG,KAAA,CAAAkK,gBAAgB,CAACc,cAAc,EAAvCI,IAAI;yBAAfnK,mBAAA,CAA2F;MAAtCsD,GAAG,EAAE6G,IAAI,CAACC;OAAM,IAAE,GAAAzK,gBAAA,CAAGwK,IAAI,CAACC,IAAI;6EAKjErL,KAAA,CAAAkK,gBAAgB,CAACoB,aAAa,IAAItL,KAAA,CAAAkK,gBAAgB,CAACoB,aAAa,CAAC3I,MAAM,Q,cAArG1B,mBAAA,CAOM,OAPNsK,WAOM,G,8BANF/L,mBAAA,CAA+C;IAA1CP,KAAK,EAAC;EAAc,GAAC,iBAAe,qBACzCO,mBAAA,CAIM,OAJNgM,YAIM,GAHFhM,mBAAA,CAEK,MAFLiM,YAEK,I,kBADDxK,mBAAA,CAA2FqD,SAAA,QAAA2B,WAAA,CAAvEjG,KAAA,CAAAkK,gBAAgB,CAACoB,aAAa,EAAvCI,KAAK;yBAAhBzK,mBAAA,CAA2F;MAAtCsD,GAAG,EAAEmH,KAAK,CAACL;wBAASK,KAAK,CAACL,IAAI;6EAKjErL,KAAA,CAAAkK,gBAAgB,CAACyB,cAAc,I,cAA7D1K,mBAAA,CAGM,OAHN2K,YAGM,G,8BAFFpM,mBAAA,CAA8C;IAAzCP,KAAK,EAAC;EAAc,GAAC,gBAAc,qBACxCO,mBAAA,CAAyE,OAAzEqM,YAAyE,EAAAjL,gBAAA,CAA5CZ,KAAA,CAAAkK,gBAAgB,CAACyB,cAAc,IAAG,MAAI,gB,wCAGvEnM,mBAAA,CAGM,OAHNsM,YAGM,G,8BAFFtM,mBAAA,CAA4C;IAAvCP,KAAK,EAAC;EAAc,GAAC,cAAY,qBACtCO,mBAAA,CAAgF,OAAhFuM,YAAgF,EAAAnL,gBAAA,CAAnDP,QAAA,CAAA2L,UAAU,CAAChM,KAAA,CAAAkK,gBAAgB,CAACrG,aAAa,kB,GAG5C7D,KAAA,CAAAkK,gBAAgB,CAAC+B,aAAa,I,cAA5DhL,mBAAA,CAGM,OAHNiL,YAGM,G,8BAFF1M,mBAAA,CAA0C;IAArCP,KAAK,EAAC;EAAc,GAAC,YAAU,qBACpCO,mBAAA,CAAgF,OAAhF2M,YAAgF,EAAAvL,gBAAA,CAAnDP,QAAA,CAAA+L,UAAU,CAACpM,KAAA,CAAAkK,gBAAgB,CAAC+B,aAAa,kB,wCAG5CjM,KAAA,CAAAkK,gBAAgB,CAACmC,iBAAiB,I,cAAhEpL,mBAAA,CAGM,OAHNqL,YAGM,G,8BAFF9M,mBAAA,CAA2C;IAAtCP,KAAK,EAAC;EAAc,GAAC,aAAW,qBACrCO,mBAAA,CAAwE,OAAxE+M,YAAwE,EAAA3L,gBAAA,CAA3CZ,KAAA,CAAAkK,gBAAgB,CAACmC,iBAAiB,iB,wCAGrCrM,KAAA,CAAAkK,gBAAgB,CAACsC,iBAAiB,I,cAAhEvL,mBAAA,CAGM,OAHNwL,YAGM,G,8BAFFjN,mBAAA,CAA0C;IAArCP,KAAK,EAAC;EAAc,GAAC,YAAU,qBACpCO,mBAAA,CAAwE,OAAxEkN,YAAwE,EAAA9L,gBAAA,CAA3CZ,KAAA,CAAAkK,gBAAgB,CAACsC,iBAAiB,iB,wCAGrCxM,KAAA,CAAAkK,gBAAgB,CAACyC,eAAe,I,cAA9D1L,mBAAA,CAGM,OAHN2L,YAGM,G,8BAFFpN,mBAAA,CAA8C;IAAzCP,KAAK,EAAC;EAAc,GAAC,gBAAc,qBACxCO,mBAAA,CAAsF,OAAtFqN,YAAsF,EAAAjM,gBAAA,CAAzDP,QAAA,CAAAyM,cAAc,CAAC9M,KAAA,CAAAkK,gBAAgB,CAACyC,eAAe,kB,wCAGlD3M,KAAA,CAAAkK,gBAAgB,CAAC6C,cAAc,IAAI/M,KAAA,CAAAkK,gBAAgB,CAAC6C,cAAc,CAACpK,MAAM,Q,cAAvG1B,mBAAA,CA8BM,OA9BN+L,YA8BM,G,8BA7BFxN,mBAAA,CAA4C;IAAvCP,KAAK,EAAC;EAAc,GAAC,cAAY,qBACtCO,mBAAA,CA2BM,OA3BNyN,YA2BM,GA1BFzN,mBAAA,CAyBQ,SAzBR0N,YAyBQ,G,8BAxBJ1N,mBAAA,CAQQ,gBAPJA,mBAAA,CAMK,aALDA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAgB,YAAZ,SAAO,E,uBAGnBA,mBAAA,CAQQ,iB,kBAPJyB,mBAAA,CAMKqD,SAAA,QAAA2B,WAAA,CANgBjG,KAAA,CAAAkK,gBAAgB,CAAC6C,cAAc,EAAzCI,MAAM;yBAAjBlM,mBAAA,CAMK;MANkDsD,GAAG,EAAE4I,MAAM,CAACC;QAC/D5N,mBAAA,CAA0B,YAAAoB,gBAAA,CAAnBuM,MAAM,CAACC,IAAI,kBAClB5N,mBAAA,CAA0B,YAAAoB,gBAAA,CAAnBuM,MAAM,CAACvL,IAAI,kBAClBpC,mBAAA,CAA4B,YAAAoB,gBAAA,CAArBuM,MAAM,CAACE,MAAM,kBACpB7N,mBAAA,CAA2C,YAAAoB,gBAAA,CAApCP,QAAA,CAAAyM,cAAc,CAACK,MAAM,CAACG,KAAK,mBAClC9N,mBAAA,CAAiD,YAAAoB,gBAAA,CAA1CP,QAAA,CAAAyM,cAAc,CAACK,MAAM,CAACI,WAAW,kB;oCAGnCvN,KAAA,CAAAkK,gBAAgB,CAACsD,YAAY,I,cAA1CvM,mBAAA,CAKQ,SAAAwM,YAAA,GAJJjO,mBAAA,CAGK,MAHLkO,YAGK,G,8BAFDlO,mBAAA,CAA8C;IAA1CmO,OAAO,EAAC;EAAG,IAACnO,mBAAA,CAAyB,gBAAjB,UAAQ,E,qBAChCA,mBAAA,CAA6E,aAAzEA,mBAAA,CAAoE,gBAAAoB,gBAAA,CAAzDP,QAAA,CAAAyM,cAAc,CAAC9M,KAAA,CAAAkK,gBAAgB,CAACsD,YAAY,kB,uFAOjDxN,KAAA,CAAAkK,gBAAgB,CAAC0D,eAAe,IAAI5N,KAAA,CAAAkK,gBAAgB,CAAC0D,eAAe,CAACjL,MAAM,Q,cAAzG1B,mBAAA,CAOM,OAPN4M,YAOM,G,8BANFrO,mBAAA,CAAkD;IAA7CP,KAAK,EAAC;EAAc,GAAC,oBAAkB,qBAC5CO,mBAAA,CAIM,OAJNsO,YAIM,GAHFtO,mBAAA,CAEK,MAFLuO,YAEK,I,kBADD9M,mBAAA,CAA2GqD,SAAA,QAAA2B,WAAA,CAAvFjG,KAAA,CAAAkK,gBAAgB,CAAC0D,eAAe,EAAzCI,KAAK;yBAAhB/M,mBAAA,CAA2G;MAApDsD,GAAG,EAAEyJ,KAAK,CAACC;wBAAgBD,KAAK,CAACC,WAAW;6EAK/GzO,mBAAA,CAOM,OAPN0O,YAOM,G,8BANF1O,mBAAA,CAA2C;IAAtCP,KAAK,EAAC;EAAc,GAAC,aAAW,qBACrCO,mBAAA,CAIM,OAJN2O,YAIM,GAHF3O,mBAAA,CAEO;IAFDP,KAAK,EAAAa,eAAA,EAAC,cAAc,EAASO,QAAA,CAAA8G,cAAc,CAACnH,KAAA,CAAAkK,gBAAgB,CAACnG,KAAK;sBACjE/D,KAAA,CAAAkK,gBAAgB,CAACnG,KAAK,wB,KAKrCvE,mBAAA,CAGM,OAHN4O,YAGM,G,8BAFF5O,mBAAA,CAAwC;IAAnCP,KAAK,EAAC;EAAc,GAAC,UAAQ,qBAClCO,mBAAA,CAAkE,OAAlE6O,YAAkE,EAAAzN,gBAAA,CAArCZ,KAAA,CAAAkK,gBAAgB,CAAC3C,WAAW,iB,GAG7D/H,mBAAA,CAGM,OAHN8O,YAGM,G,8BAFF9O,mBAAA,CAA6C;IAAxCP,KAAK,EAAC;EAAc,GAAC,eAAa,qBACvCO,mBAAA,CAAuE,OAAvE+O,YAAuE,EAAA3N,gBAAA,CAA1CZ,KAAA,CAAAkK,gBAAgB,CAACxC,gBAAgB,iB,GAGlElI,mBAAA,CAGM,OAHNgP,YAGM,G,8BAFFhP,mBAAA,CAA6C;IAAxCP,KAAK,EAAC;EAAc,GAAC,eAAa,qBACvCO,mBAAA,CAA6E,OAA7EiP,YAA6E,EAAA7N,gBAAA,CAAhDP,QAAA,CAAA2L,UAAU,CAAChM,KAAA,CAAAkK,gBAAgB,CAACwE,UAAU,kB,OAK/ElP,mBAAA,CAgBM,OAhBNmP,YAgBM,GAfFnP,mBAAA,CAKS;IALAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAAuO,aAAA,IAAAvO,QAAA,CAAAuO,aAAA,IAAApO,IAAA,CAAa;IAAEvB,KAAK,EAAC;qBACjCgC,mBAAA,CAEM,OAFN4N,YAEM,OAAA1O,MAAA,UAAAA,MAAA,SADFX,mBAAA,CAAsK;IAAhKN,CAAC,EAAC;EAA4J,0B,sDAClK,SAEV,oB,GACAM,mBAAA,CAKS;IALAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAAyO,YAAA,IAAAzO,QAAA,CAAAyO,YAAA,IAAAtO,IAAA,CAAY;IAAEvB,KAAK,EAAC;qBAChCgC,mBAAA,CAEM,OAFN8N,YAEM,OAAA5O,MAAA,UAAAA,MAAA,SADFX,mBAAA,CAAmG;IAA7FN,CAAC,EAAC;EAAyF,0B,sDAC/F,mBAEV,oB,GACAM,mBAAA,CAES;IAFAU,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAK,IAAA,KAAEH,QAAA,CAAA8J,kBAAA,IAAA9J,QAAA,CAAA8J,kBAAA,IAAA3J,IAAA,CAAkB;IAAEvB,KAAK,EAAC;KAAkB,SAE5D,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}