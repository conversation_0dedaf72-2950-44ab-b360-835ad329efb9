<script>
export default {
  name: 'LoginView',
  data() {
    return {
      username: '',
      password: '',
      team_pin: '',
      busy: false,
      error: ''
    };
  },
  mounted() {
    // Aggressive cache busting for login component
    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);
    
    // Add cache-busting meta tags specifically for login
    const metaTags = [
      { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },
      { name: 'pragma', content: 'no-cache' },
      { name: 'expires', content: '0' },
      { name: 'last-modified', content: new Date().toUTCString() },
      { name: 'etag', content: cacheBuster },
      { name: 'login-cache-buster', content: cacheBuster }
    ];
    
    metaTags.forEach(tag => {
      // Remove existing meta tags with same name
      const existing = document.querySelector(`meta[http-equiv="${tag.name}"]`);
      if (existing) existing.remove();
      
      // Add new meta tag
      const meta = document.createElement('meta');
      meta.setAttribute('http-equiv', tag.name);
      meta.setAttribute('content', tag.content);
      document.head.appendChild(meta);
    });
    
    // Force component refresh to prevent caching issues
    this.$forceUpdate();
    
    // Clear any cached form data
    this.username = '';
    this.password = '';
    this.team_pin = '';
    this.error = '';
    
    // Clear only non-authentication browser storage
    if (typeof Storage !== 'undefined') {
      // Only clear sessionStorage, preserve localStorage auth tokens
      sessionStorage.clear();
    }
    
    // Force browser to not cache this page
    if (window.history && window.history.replaceState) {
      const url = new URL(window.location.href);
      url.searchParams.set('_cb', cacheBuster);
      url.searchParams.set('_nocache', '1');
      window.history.replaceState(null, null, url.toString());
    }
    
    // Add cache-busting attribute to component element
    if (this.$el && this.$el.setAttribute) {
      this.$el.setAttribute('data-login-cache-bust', cacheBuster);
      this.$el.setAttribute('data-no-cache', 'true');
    }
  },
  beforeUnmount() {
    // Clear form data when component is destroyed
    this.username = '';
    this.password = '';
    this.team_pin = '';
    this.error = '';
  },
  methods: {
    async doLogin() {
      this.error = '';
      if (!this.username || !this.password || !this.team_pin) {
        this.error = 'يرجى تعبئة جميع الحقول';
        return;
      }
      this.busy = true;
      try {
        const res = await fetch('/api/v1/ndyt-activities/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username: this.username, password: this.password, team_pin: this.team_pin })
        });
        const data = await res.json();
        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');
        localStorage.setItem('ndyt_token', data.token);
        // optional: store user info
        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));
        // store team pin for later submissions
        localStorage.setItem('ndyt_team_pin', this.team_pin);
        const redirect = this.$route.query.redirect || '/';
        this.$router.replace(redirect);
      } catch (e) {
        this.error = e.message;
      } finally {
        this.busy = false;
      }
    }
  }
}
</script>
<template>
  <div class="login-container">
    <!-- Animated Background -->
    <div class="animated-bg">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <!-- Header Section -->
    <header class="header-section">
      <div class="header-content">
        <div class="logo-container">
          <img class="logo" src="../assets/ndyt_logo.jpg" alt="شعار الفريق">
          <div class="logo-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="org-name">المجلس الأعلى للشباب</h2>
          <h3 class="team-name">الفريق الوطني للشباب الرقمي</h3>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="login-card">
        <div class="card-header">
          <h1 class="card-title">تسجيل الدخول</h1>
          <p class="card-subtitle">مرحباً بك مرة أخرى</p>
        </div>

        <form class="login-form" @submit.prevent="doLogin">
          <div class="input-group">
            <div class="input-wrapper">
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/>
                </svg>
              </div>
              <input
                v-model="username"
                type="text"
                dir="rtl"
                placeholder=""
                class="form-input"
                required
              />
              <label class="floating-label">اسم المستخدم</label>
            </div>
          </div>

          <div class="input-group">
            <div class="input-wrapper">
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z" fill="currentColor"/>
                </svg>
              </div>
              <input
                v-model="password"
                type="password"
                dir="rtl"
                placeholder=""
                class="form-input"
                required
              />
              <label class="floating-label">كلمة المرور</label>
            </div>
          </div>

          <div class="input-group">
            <div class="input-wrapper">
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <input
                v-model="team_pin"
                type="password"
                dir="rtl"
                placeholder=""
                class="form-input"
                required
              />
              <label class="floating-label">رمز الفريق الرقمي</label>
            </div>
          </div>

          <button type="submit" :disabled="busy" class="login-btn">
            <span v-if="!busy" class="btn-content">
              <span class="btn-text">دخول</span>
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 7L19 12M19 12L14 17M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span v-else class="btn-loading">
              <div class="loading-spinner"></div>
              <span>جاري الدخول...</span>
            </span>
          </button>

          <div v-if="error" class="error-message">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor"/>
            </svg>
            <span>{{ error }}</span>
          </div>
        </form>

        <div class="card-footer">
          <p class="register-link">
            ليس لديك حساب؟
            <router-link to="/register" class="link">سجل الآن</router-link>
          </p>
        </div>
      </div>
    </main>
  </div>
</template>
<style scoped>
/* Global Styles */
* {
  box-sizing: border-box;
}

.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  direction: rtl;
}

/* Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 10%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

/* Header Section */
.header-section {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 3px solid rgba(79, 70, 229, 0.5);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
}

.header-text {
  text-align: center;
}

.org-name {
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  font-weight: 800;
  color: #f8fafc;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.team-name {
  font-size: clamp(1rem, 3vw, 1.4rem);
  font-weight: 600;
  color: #cbd5e1;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 140px);
  padding: 2rem 1rem;
}

.login-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 24px;
  padding: 2.5rem;
  width: 100%;
  max-width: 480px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);
}

.login-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Card Header */
.card-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.login-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
  animation: iconFloat 3s ease-in-out infinite;
}

.login-icon svg {
  width: 32px;
  height: 32px;
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

.card-title {
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-subtitle {
  color: #94a3b8;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  position: relative;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #64748b;
  z-index: 2;
  transition: color 0.3s ease;
}

.form-input {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: #f1f5f9;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
}

.form-input:focus {
  outline: none;
  border-color: #4f46e5;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
}

.form-input:focus + .floating-label,
.form-input:not(:placeholder-shown) + .floating-label {
  transform: translateY(-2.5rem) scale(0.85);
  color: #ffffff;
  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);
}

.form-input:focus ~ .input-icon {
  color: #4f46e5;
}

.floating-label {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  pointer-events: none;
  padding: 0 0.5rem;
}

/* Button Styles */
.login-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
  margin-top: 1rem;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:hover {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon {
  transform: translateX(-4px);
}

.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  color: #fca5a5;
  font-size: 0.9rem;
  margin-top: 1rem;
  animation: slideIn 0.3s ease;
}

.error-message svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Footer */
.card-footer {
  margin-top: 2rem;
  text-align: center;
}

.register-link {
  color: #94a3b8;
  font-size: 0.95rem;
  margin: 0;
}

.link {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}

.link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transition: width 0.3s ease;
}

.link:hover::after {
  width: 100%;
}

.link:hover {
  color: #6366f1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .logo {
    width: 60px;
    height: 60px;
  }

  .logo-glow {
    width: 80px;
    height: 80px;
  }

  .main-content {
    padding: 1.5rem 1rem;
    min-height: calc(100vh - 120px);
  }

  .login-card {
    padding: 2rem;
    border-radius: 20px;
  }

  .card-title {
    font-size: 1.75rem;
  }

  .login-icon {
    width: 56px;
    height: 56px;
    margin-bottom: 1rem;
  }

  .login-icon svg {
    width: 28px;
    height: 28px;
  }

  .form-input {
    padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .login-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .header-section {
    padding: 0.75rem;
  }

  .logo {
    width: 50px;
    height: 50px;
  }

  .logo-glow {
    width: 70px;
    height: 70px;
  }

  .main-content {
    padding: 1rem 0.75rem;
  }

  .login-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .card-title {
    font-size: 1.5rem;
  }

  .card-subtitle {
    font-size: 0.9rem;
  }

  .login-icon {
    width: 48px;
    height: 48px;
    border-radius: 16px;
  }

  .login-icon svg {
    width: 24px;
    height: 24px;
  }

  .login-form {
    gap: 1.25rem;
  }

  .form-input {
    padding: 0.75rem 2.25rem 0.75rem 0.75rem;
    border-radius: 12px;
  }

  .input-icon {
    left: 0.75rem;
    width: 18px;
    height: 18px;
  }

  .floating-label {
    right: 0.75rem;
    font-size: 0.9rem;
  }

  .login-btn {
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
  }

  .btn-icon {
    width: 18px;
    height: 18px;
  }

  .error-message {
    padding: 0.75rem;
    font-size: 0.85rem;
  }

  .register-link {
    font-size: 0.9rem;
  }
}

@media (max-width: 360px) {
  .login-card {
    padding: 1.25rem;
  }

  .card-header {
    margin-bottom: 2rem;
  }

  .card-title {
    font-size: 1.375rem;
  }

  .form-input {
    font-size: 14px;
  }

  .floating-label {
    font-size: 0.85rem;
  }
}
</style>