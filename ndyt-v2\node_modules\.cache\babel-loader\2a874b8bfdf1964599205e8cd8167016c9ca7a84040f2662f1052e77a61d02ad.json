{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelText as _vModelText, withDirectives as _withDirectives, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, withModifiers as _withModifiers, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../assets/ndyt_logo.jpg';\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"main-content\"\n};\nconst _hoisted_3 = {\n  class: \"login-card\"\n};\nconst _hoisted_4 = {\n  class: \"input-group\"\n};\nconst _hoisted_5 = {\n  class: \"input-wrapper\"\n};\nconst _hoisted_6 = {\n  class: \"input-group\"\n};\nconst _hoisted_7 = {\n  class: \"input-wrapper\"\n};\nconst _hoisted_8 = {\n  class: \"input-group\"\n};\nconst _hoisted_9 = {\n  class: \"input-wrapper\"\n};\nconst _hoisted_10 = [\"disabled\"];\nconst _hoisted_11 = {\n  key: 0,\n  class: \"btn-content\"\n};\nconst _hoisted_12 = {\n  key: 1,\n  class: \"btn-loading\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"error-message\"\n};\nconst _hoisted_14 = {\n  class: \"card-footer\"\n};\nconst _hoisted_15 = {\n  class: \"register-link\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[16] || (_cache[16] = _createStaticVNode(\"<div class=\\\"animated-bg\\\" data-v-cdff2924><div class=\\\"floating-shapes\\\" data-v-cdff2924><div class=\\\"shape shape-1\\\" data-v-cdff2924></div><div class=\\\"shape shape-2\\\" data-v-cdff2924></div><div class=\\\"shape shape-3\\\" data-v-cdff2924></div><div class=\\\"shape shape-4\\\" data-v-cdff2924></div><div class=\\\"shape shape-5\\\" data-v-cdff2924></div></div></div><header class=\\\"header-section\\\" data-v-cdff2924><div class=\\\"header-content\\\" data-v-cdff2924><div class=\\\"logo-container\\\" data-v-cdff2924><img class=\\\"logo\\\" src=\\\"\" + _imports_0 + \"\\\" alt=\\\"شعار الفريق\\\" data-v-cdff2924><div class=\\\"logo-glow\\\" data-v-cdff2924></div></div><div class=\\\"header-text\\\" data-v-cdff2924><h2 class=\\\"org-name\\\" data-v-cdff2924>المجلس الأعلى للشباب</h2><h3 class=\\\"team-name\\\" data-v-cdff2924>الفريق الوطني للشباب الرقمي</h3></div></div></header>\", 2)), _createElementVNode(\"main\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"card-title\"\n  }, \"تسجيل الدخول\"), _createElementVNode(\"p\", {\n    class: \"card-subtitle\"\n  }, \"مرحباً بك مرة أخرى\")], -1)), _createElementVNode(\"form\", {\n    class: \"login-form\",\n    onSubmit: _cache[3] || (_cache[3] = _withModifiers((...args) => $options.doLogin && $options.doLogin(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z\",\n    fill: \"currentColor\"\n  })])], -1)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    type: \"text\",\n    dir: \"rtl\",\n    placeholder: \"\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512), [[_vModelText, $data.username]]), _cache[5] || (_cache[5] = _createElementVNode(\"label\", {\n    class: \"floating-label\"\n  }, \"اسم المستخدم\", -1))])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z\",\n    fill: \"currentColor\"\n  })])], -1)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.password = $event),\n    type: \"password\",\n    dir: \"rtl\",\n    placeholder: \"\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512), [[_vModelText, $data.password]]), _cache[7] || (_cache[7] = _createElementVNode(\"label\", {\n    class: \"floating-label\"\n  }, \"كلمة المرور\", -1))])]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  })])], -1)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.team_pin = $event),\n    type: \"text\",\n    dir: \"rtl\",\n    placeholder: \"\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512), [[_vModelText, $data.team_pin]]), _cache[9] || (_cache[9] = _createElementVNode(\"label\", {\n    class: \"floating-label\"\n  }, \"رمز الفريق الرقمي\", -1))])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.busy,\n    class: \"login-btn\"\n  }, [!$data.busy ? (_openBlock(), _createElementBlock(\"span\", _hoisted_11, [...(_cache[10] || (_cache[10] = [_createElementVNode(\"span\", {\n    class: \"btn-text\"\n  }, \"دخول\", -1), _createElementVNode(\"svg\", {\n    class: \"btn-icon\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M14 7L19 12M19 12L14 17M19 12H5\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  })], -1)]))])) : (_openBlock(), _createElementBlock(\"span\", _hoisted_12, [...(_cache[11] || (_cache[11] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, null, -1), _createElementVNode(\"span\", null, \"جاري الدخول...\", -1)]))]))], 8, _hoisted_10), $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_cache[12] || (_cache[12] = _createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z\",\n    fill: \"currentColor\"\n  })], -1)), _createElementVNode(\"span\", null, _toDisplayString($data.error), 1)])) : _createCommentVNode(\"\", true)], 32), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"p\", _hoisted_15, [_cache[14] || (_cache[14] = _createTextVNode(\" ليس لديك حساب؟ \", -1)), _createVNode(_component_router_link, {\n    to: \"/register\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => [...(_cache[13] || (_cache[13] = [_createTextVNode(\"سجل الآن\", -1)]))]),\n    _: 1\n  })])])])])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onSubmit", "_cache", "_withModifiers", "args", "$options", "do<PERSON><PERSON><PERSON>", "_hoisted_4", "_hoisted_5", "viewBox", "fill", "xmlns", "d", "$data", "username", "$event", "type", "dir", "placeholder", "required", "_hoisted_6", "_hoisted_7", "password", "_hoisted_8", "_hoisted_9", "stroke", "team_pin", "disabled", "busy", "_hoisted_11", "_hoisted_12", "error", "_hoisted_13", "_toDisplayString", "_hoisted_14", "_hoisted_15", "_createVNode", "_component_router_link", "to"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\LoginView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'LoginView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      team_pin: '',\r\n      busy: false,\r\n      error: ''\r\n    };\r\n  },\r\n  mounted() {\r\n    // Aggressive cache busting for login component\r\n    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\r\n    \r\n    // Add cache-busting meta tags specifically for login\r\n    const metaTags = [\r\n      { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },\r\n      { name: 'pragma', content: 'no-cache' },\r\n      { name: 'expires', content: '0' },\r\n      { name: 'last-modified', content: new Date().toUTCString() },\r\n      { name: 'etag', content: cacheBuster },\r\n      { name: 'login-cache-buster', content: cacheBuster }\r\n    ];\r\n    \r\n    metaTags.forEach(tag => {\r\n      // Remove existing meta tags with same name\r\n      const existing = document.querySelector(`meta[http-equiv=\"${tag.name}\"]`);\r\n      if (existing) existing.remove();\r\n      \r\n      // Add new meta tag\r\n      const meta = document.createElement('meta');\r\n      meta.setAttribute('http-equiv', tag.name);\r\n      meta.setAttribute('content', tag.content);\r\n      document.head.appendChild(meta);\r\n    });\r\n    \r\n    // Force component refresh to prevent caching issues\r\n    this.$forceUpdate();\r\n    \r\n    // Clear any cached form data\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n    \r\n    // Clear only non-authentication browser storage\r\n    if (typeof Storage !== 'undefined') {\r\n      // Only clear sessionStorage, preserve localStorage auth tokens\r\n      sessionStorage.clear();\r\n    }\r\n    \r\n    // Force browser to not cache this page\r\n    if (window.history && window.history.replaceState) {\r\n      const url = new URL(window.location.href);\r\n      url.searchParams.set('_cb', cacheBuster);\r\n      url.searchParams.set('_nocache', '1');\r\n      window.history.replaceState(null, null, url.toString());\r\n    }\r\n    \r\n    // Add cache-busting attribute to component element\r\n    if (this.$el && this.$el.setAttribute) {\r\n      this.$el.setAttribute('data-login-cache-bust', cacheBuster);\r\n      this.$el.setAttribute('data-no-cache', 'true');\r\n    }\r\n  },\r\n  beforeUnmount() {\r\n    // Clear form data when component is destroyed\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n  },\r\n  methods: {\r\n    async doLogin() {\r\n      this.error = '';\r\n      if (!this.username || !this.password || !this.team_pin) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/login', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, password: this.password, team_pin: this.team_pin })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        // optional: store user info\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        // store team pin for later submissions\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"login-container\">\r\n    <!-- Animated Background -->\r\n    <div class=\"animated-bg\">\r\n      <div class=\"floating-shapes\">\r\n        <div class=\"shape shape-1\"></div>\r\n        <div class=\"shape shape-2\"></div>\r\n        <div class=\"shape shape-3\"></div>\r\n        <div class=\"shape shape-4\"></div>\r\n        <div class=\"shape shape-5\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Header Section -->\r\n    <header class=\"header-section\">\r\n      <div class=\"header-content\">\r\n        <div class=\"logo-container\">\r\n          <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n          <div class=\"logo-glow\"></div>\r\n        </div>\r\n        <div class=\"header-text\">\r\n          <h2 class=\"org-name\">المجلس الأعلى للشباب</h2>\r\n          <h3 class=\"team-name\">الفريق الوطني للشباب الرقمي</h3>\r\n        </div>\r\n      </div>\r\n    </header>\r\n\r\n    <!-- Main Content -->\r\n    <main class=\"main-content\">\r\n      <div class=\"login-card\">\r\n        <div class=\"card-header\">\r\n          <h1 class=\"card-title\">تسجيل الدخول</h1>\r\n          <p class=\"card-subtitle\">مرحباً بك مرة أخرى</p>\r\n        </div>\r\n\r\n        <form class=\"login-form\" @submit.prevent=\"doLogin\">\r\n          <div class=\"input-group\">\r\n            <div class=\"input-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z\" fill=\"currentColor\"/>\r\n                </svg>\r\n              </div>\r\n              <input\r\n                v-model=\"username\"\r\n                type=\"text\"\r\n                dir=\"rtl\"\r\n                placeholder=\"\"\r\n                class=\"form-input\"\r\n                required\r\n              />\r\n              <label class=\"floating-label\">اسم المستخدم</label>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group\">\r\n            <div class=\"input-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z\" fill=\"currentColor\"/>\r\n                </svg>\r\n              </div>\r\n              <input\r\n                v-model=\"password\"\r\n                type=\"password\"\r\n                dir=\"rtl\"\r\n                placeholder=\"\"\r\n                class=\"form-input\"\r\n                required\r\n              />\r\n              <label class=\"floating-label\">كلمة المرور</label>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group\">\r\n            <div class=\"input-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </div>\r\n              <input\r\n                v-model=\"team_pin\"\r\n                type=\"text\"\r\n                dir=\"rtl\"\r\n                placeholder=\"\"\r\n                class=\"form-input\"\r\n                required\r\n              />\r\n              <label class=\"floating-label\">رمز الفريق الرقمي</label>\r\n            </div>\r\n          </div>\r\n\r\n          <button type=\"submit\" :disabled=\"busy\" class=\"login-btn\">\r\n            <span v-if=\"!busy\" class=\"btn-content\">\r\n              <span class=\"btn-text\">دخول</span>\r\n              <svg class=\"btn-icon\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M14 7L19 12M19 12L14 17M19 12H5\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n            </span>\r\n            <span v-else class=\"btn-loading\">\r\n              <div class=\"loading-spinner\"></div>\r\n              <span>جاري الدخول...</span>\r\n            </span>\r\n          </button>\r\n\r\n          <div v-if=\"error\" class=\"error-message\">\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z\" fill=\"currentColor\"/>\r\n            </svg>\r\n            <span>{{ error }}</span>\r\n          </div>\r\n        </form>\r\n\r\n        <div class=\"card-footer\">\r\n          <p class=\"register-link\">\r\n            ليس لديك حساب؟\r\n            <router-link to=\"/register\" class=\"link\">سجل الآن</router-link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n/* Global Styles */\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.login-container {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\r\n  direction: rtl;\r\n}\r\n\r\n/* Animated Background */\r\n.animated-bg {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.floating-shapes {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shape {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  background: linear-gradient(45deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));\r\n  animation: float 6s ease-in-out infinite;\r\n}\r\n\r\n.shape-1 {\r\n  width: 80px;\r\n  height: 80px;\r\n  top: 10%;\r\n  left: 10%;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.shape-2 {\r\n  width: 120px;\r\n  height: 120px;\r\n  top: 20%;\r\n  right: 15%;\r\n  animation-delay: 2s;\r\n}\r\n\r\n.shape-3 {\r\n  width: 60px;\r\n  height: 60px;\r\n  bottom: 30%;\r\n  left: 20%;\r\n  animation-delay: 4s;\r\n}\r\n\r\n.shape-4 {\r\n  width: 100px;\r\n  height: 100px;\r\n  bottom: 20%;\r\n  right: 10%;\r\n  animation-delay: 1s;\r\n}\r\n\r\n.shape-5 {\r\n  width: 140px;\r\n  height: 140px;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation-delay: 3s;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px) rotate(0deg);\r\n    opacity: 0.5;\r\n  }\r\n  50% {\r\n    transform: translateY(-20px) rotate(180deg);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n/* Header Section */\r\n.header-section {\r\n  position: relative;\r\n  z-index: 2;\r\n  padding: 1.5rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  backdrop-filter: blur(10px);\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 2rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.logo-container {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.logo {\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(79, 70, 229, 0.5);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.logo-glow {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 50%;\r\n  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    transform: translate(-50%, -50%) scale(1);\r\n    opacity: 0.5;\r\n  }\r\n  50% {\r\n    transform: translate(-50%, -50%) scale(1.1);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n.header-text {\r\n  text-align: center;\r\n}\r\n\r\n.org-name {\r\n  font-size: clamp(1.2rem, 4vw, 1.8rem);\r\n  font-weight: 800;\r\n  color: #f8fafc;\r\n  margin: 0 0 0.5rem 0;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.team-name {\r\n  font-size: clamp(1rem, 3vw, 1.4rem);\r\n  font-weight: 600;\r\n  color: #cbd5e1;\r\n  margin: 0;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n  position: relative;\r\n  z-index: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: calc(100vh - 140px);\r\n  padding: 2rem 1rem;\r\n}\r\n\r\n.login-card {\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.15);\r\n  border-radius: 24px;\r\n  padding: 2.5rem;\r\n  width: 100%;\r\n  max-width: 480px;\r\n  box-shadow:\r\n    0 20px 40px rgba(0, 0, 0, 0.4),\r\n    0 0 0 1px rgba(255, 255, 255, 0.05);\r\n  transition: all 0.4s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.login-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);\r\n}\r\n\r\n.login-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow:\r\n    0 32px 64px rgba(0, 0, 0, 0.5),\r\n    0 0 0 1px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Card Header */\r\n.card-header {\r\n  text-align: center;\r\n  margin-bottom: 2.5rem;\r\n}\r\n\r\n.login-icon {\r\n  width: 64px;\r\n  height: 64px;\r\n  margin: 0 auto 1.5rem;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);\r\n  animation: iconFloat 3s ease-in-out infinite;\r\n}\r\n\r\n.login-icon svg {\r\n  width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n@keyframes iconFloat {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n.card-title {\r\n  font-size: 2rem;\r\n  font-weight: 800;\r\n  margin: 0 0 0.5rem 0;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.card-subtitle {\r\n  color: #94a3b8;\r\n  font-size: 1rem;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Form Styles */\r\n.login-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.input-group {\r\n  position: relative;\r\n}\r\n\r\n.input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.input-icon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  color: #64748b;\r\n  z-index: 2;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  padding: 1rem 3rem 1rem 1rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 16px;\r\n  color: #f1f5f9;\r\n  font-size: 1rem;\r\n  transition: all 0.3s ease;\r\n  text-align: right;\r\n  direction: rtl;\r\n}\r\n\r\n.form-input:focus {\r\n  outline: none;\r\n  border-color: #4f46e5;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);\r\n}\r\n\r\n.form-input:focus + .floating-label,\r\n.form-input:not(:placeholder-shown) + .floating-label {\r\n  transform: translateY(-2.5rem) scale(0.85);\r\n  color: #ffffff;\r\n  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);\r\n}\r\n\r\n.form-input:focus ~ .input-icon {\r\n  color: #4f46e5;\r\n}\r\n\r\n.floating-label {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #64748b;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  pointer-events: none;\r\n  padding: 0 0.5rem;\r\n}\r\n\r\n/* Button Styles */\r\n.login-btn {\r\n  width: 100%;\r\n  padding: 1rem 2rem;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  border: none;\r\n  border-radius: 16px;\r\n  color: white;\r\n  font-size: 1.1rem;\r\n  font-weight: 700;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);\r\n  margin-top: 1rem;\r\n}\r\n\r\n.login-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.login-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.login-btn:hover {\r\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.login-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.login-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.btn-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.btn-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.login-btn:hover .btn-icon {\r\n  transform: translateX(-4px);\r\n}\r\n\r\n.btn-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-top: 2px solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Error Message */\r\n.error-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem;\r\n  background: rgba(239, 68, 68, 0.1);\r\n  border: 1px solid rgba(239, 68, 68, 0.3);\r\n  border-radius: 12px;\r\n  color: #fca5a5;\r\n  font-size: 0.9rem;\r\n  margin-top: 1rem;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n.error-message svg {\r\n  width: 20px;\r\n  height: 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Card Footer */\r\n.card-footer {\r\n  margin-top: 2rem;\r\n  text-align: center;\r\n}\r\n\r\n.register-link {\r\n  color: #94a3b8;\r\n  font-size: 0.95rem;\r\n  margin: 0;\r\n}\r\n\r\n.link {\r\n  color: #4f46e5;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.link::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: 0;\r\n  width: 0;\r\n  height: 2px;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.link:hover::after {\r\n  width: 100%;\r\n}\r\n\r\n.link:hover {\r\n  color: #6366f1;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .header-section {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1.5rem 1rem;\r\n    min-height: calc(100vh - 120px);\r\n  }\r\n\r\n  .login-card {\r\n    padding: 2rem;\r\n    border-radius: 20px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .login-icon {\r\n    width: 56px;\r\n    height: 56px;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .login-icon svg {\r\n    width: 28px;\r\n    height: 28px;\r\n  }\r\n\r\n  .form-input {\r\n    padding: 0.875rem 2.5rem 0.875rem 0.875rem;\r\n    font-size: 16px; /* Prevents zoom on iOS */\r\n  }\r\n\r\n  .login-btn {\r\n    padding: 0.875rem 1.5rem;\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .header-section {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 70px;\r\n    height: 70px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1rem 0.75rem;\r\n  }\r\n\r\n  .login-card {\r\n    padding: 1.5rem;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .card-subtitle {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .login-icon {\r\n    width: 48px;\r\n    height: 48px;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .login-icon svg {\r\n    width: 24px;\r\n    height: 24px;\r\n  }\r\n\r\n  .login-form {\r\n    gap: 1.25rem;\r\n  }\r\n\r\n  .form-input {\r\n    padding: 0.75rem 2.25rem 0.75rem 0.75rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .input-icon {\r\n    left: 0.75rem;\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .floating-label {\r\n    right: 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .login-btn {\r\n    padding: 0.75rem 1.25rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .btn-icon {\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .error-message {\r\n    padding: 0.75rem;\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .register-link {\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 360px) {\r\n  .login-card {\r\n    padding: 1.25rem;\r\n  }\r\n\r\n  .card-header {\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.375rem;\r\n  }\r\n\r\n  .form-input {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .floating-label {\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n</style>"], "mappings": ";OA2H4BA,UAA6B;;EAhBlDC,KAAK,EAAC;AAAiB;;EA2BpBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAY;;EAOdA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;EAkBvBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;EAkBvBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;;;EAmBPA,KAAK,EAAC;;;;EAMZA,KAAK,EAAC;;;;EAMHA,KAAK,EAAC;;;EAQrBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAe;;;uBAlHhCC,mBAAA,CAyHM,OAzHNC,UAyHM,G,03BA9FJC,mBAAA,CA6FO,QA7FPC,UA6FO,GA5FLD,mBAAA,CA2FM,OA3FNE,UA2FM,G,4BA1FJF,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAwC;IAApCH,KAAK,EAAC;EAAY,GAAC,cAAY,GACnCG,mBAAA,CAA+C;IAA5CH,KAAK,EAAC;EAAe,GAAC,oBAAkB,E,QAG7CG,mBAAA,CA6EO;IA7EDH,KAAK,EAAC,YAAY;IAAEM,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,OAAA,IAAAD,QAAA,CAAAC,OAAA,IAAAF,IAAA,CAAO;MAC/CN,mBAAA,CAiBM,OAjBNS,UAiBM,GAhBJT,mBAAA,CAeM,OAfNU,UAeM,G,0BAdJV,mBAAA,CAIM;IAJDH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAEM;IAFDW,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCb,mBAAA,CAAwL;IAAlLc,CAAC,EAAC,0JAA0J;IAACF,IAAI,EAAC;+BAG5KZ,mBAAA,CAOE;+DANSe,KAAA,CAAAC,QAAQ,GAAAC,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXC,GAAG,EAAC,KAAK;IACTC,WAAW,EAAC,EAAE;IACdvB,KAAK,EAAC,YAAY;IAClBwB,QAAQ,EAAR;gCALSN,KAAA,CAAAC,QAAQ,E,6BAOnBhB,mBAAA,CAAkD;IAA3CH,KAAK,EAAC;EAAgB,GAAC,cAAY,O,KAI9CG,mBAAA,CAiBM,OAjBNsB,UAiBM,GAhBJtB,mBAAA,CAeM,OAfNuB,UAeM,G,0BAdJvB,mBAAA,CAIM;IAJDH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAEM;IAFDW,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCb,mBAAA,CAA0S;IAApSc,CAAC,EAAC,4QAA4Q;IAACF,IAAI,EAAC;+BAG9RZ,mBAAA,CAOE;+DANSe,KAAA,CAAAS,QAAQ,GAAAP,MAAA;IACjBC,IAAI,EAAC,UAAU;IACfC,GAAG,EAAC,KAAK;IACTC,WAAW,EAAC,EAAE;IACdvB,KAAK,EAAC,YAAY;IAClBwB,QAAQ,EAAR;gCALSN,KAAA,CAAAS,QAAQ,E,6BAOnBxB,mBAAA,CAAiD;IAA1CH,KAAK,EAAC;EAAgB,GAAC,aAAW,O,KAI7CG,mBAAA,CAiBM,OAjBNyB,UAiBM,GAhBJzB,mBAAA,CAeM,OAfN0B,UAeM,G,0BAdJ1B,mBAAA,CAIM;IAJDH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAEM;IAFDW,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCb,mBAAA,CAA8J;IAAxJc,CAAC,EAAC,8DAA8D;IAACa,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;+BAGzJ3B,mBAAA,CAOE;+DANSe,KAAA,CAAAa,QAAQ,GAAAX,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXC,GAAG,EAAC,KAAK;IACTC,WAAW,EAAC,EAAE;IACdvB,KAAK,EAAC,YAAY;IAClBwB,QAAQ,EAAR;gCALSN,KAAA,CAAAa,QAAQ,E,6BAOnB5B,mBAAA,CAAuD;IAAhDH,KAAK,EAAC;EAAgB,GAAC,mBAAiB,O,KAInDG,mBAAA,CAWS;IAXDkB,IAAI,EAAC,QAAQ;IAAEW,QAAQ,EAAEd,KAAA,CAAAe,IAAI;IAAEjC,KAAK,EAAC;OAC9BkB,KAAA,CAAAe,IAAI,I,cAAjBhC,mBAAA,CAKO,QALPiC,WAKO,OAAA3B,MAAA,SAAAA,MAAA,QAJLJ,mBAAA,CAAkC;IAA5BH,KAAK,EAAC;EAAU,GAAC,MAAI,OAC3BG,mBAAA,CAEM;IAFDH,KAAK,EAAC,UAAU;IAACc,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MAC1Db,mBAAA,CAAiI;IAA3Hc,CAAC,EAAC,iCAAiC;IAACa,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;kCAG5H7B,mBAAA,CAGO,QAHPkC,WAGO,OAAA5B,MAAA,SAAAA,MAAA,QAFLJ,mBAAA,CAAmC;IAA9BH,KAAK,EAAC;EAAiB,cAC5BG,mBAAA,CAA2B,cAArB,gBAAc,M,yBAIbe,KAAA,CAAAkB,KAAK,I,cAAhBnC,mBAAA,CAKM,OALNoC,WAKM,G,4BAJJlC,mBAAA,CAEM;IAFDW,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCb,mBAAA,CAAwI;IAAlIc,CAAC,EAAC,0GAA0G;IAACF,IAAI,EAAC;aAE1HZ,mBAAA,CAAwB,cAAAmC,gBAAA,CAAfpB,KAAA,CAAAkB,KAAK,M,0CAIlBjC,mBAAA,CAKM,OALNoC,WAKM,GAJJpC,mBAAA,CAGI,KAHJqC,WAGI,G,6CAHqB,kBAEvB,QAAAC,YAAA,CAA+DC,sBAAA;IAAlDC,EAAE,EAAC,WAAW;IAAC3C,KAAK,EAAC;;sBAAO,MAAQ,KAAAO,MAAA,SAAAA,MAAA,Q,iBAAR,UAAQ,M", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}