{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelText as _vModelText, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, vModelSelect as _vModelSelect, withModifiers as _withModifiers, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../assets/ndyt_logo.jpg';\nconst _hoisted_1 = {\n  class: \"register-container\"\n};\nconst _hoisted_2 = {\n  class: \"main-content\"\n};\nconst _hoisted_3 = {\n  class: \"register-card\"\n};\nconst _hoisted_4 = {\n  class: \"form-row\"\n};\nconst _hoisted_5 = {\n  class: \"input-group\"\n};\nconst _hoisted_6 = {\n  class: \"input-wrapper\"\n};\nconst _hoisted_7 = {\n  class: \"input-group\"\n};\nconst _hoisted_8 = {\n  class: \"input-wrapper\"\n};\nconst _hoisted_9 = {\n  class: \"input-group\"\n};\nconst _hoisted_10 = {\n  class: \"select-wrapper\"\n};\nconst _hoisted_11 = [\"value\"];\nconst _hoisted_12 = {\n  class: \"form-row\"\n};\nconst _hoisted_13 = {\n  class: \"input-group\"\n};\nconst _hoisted_14 = {\n  class: \"input-wrapper\"\n};\nconst _hoisted_15 = {\n  class: \"input-group\"\n};\nconst _hoisted_16 = {\n  class: \"input-wrapper\"\n};\nconst _hoisted_17 = [\"disabled\"];\nconst _hoisted_18 = {\n  key: 0,\n  class: \"btn-content\"\n};\nconst _hoisted_19 = {\n  key: 1,\n  class: \"btn-loading\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"error-message\"\n};\nconst _hoisted_21 = {\n  class: \"card-footer\"\n};\nconst _hoisted_22 = {\n  class: \"login-link\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Animated Background \"), _cache[23] || (_cache[23] = _createStaticVNode(\"<div class=\\\"animated-bg\\\" data-v-03589122><div class=\\\"floating-shapes\\\" data-v-03589122><div class=\\\"shape shape-1\\\" data-v-03589122></div><div class=\\\"shape shape-2\\\" data-v-03589122></div><div class=\\\"shape shape-3\\\" data-v-03589122></div><div class=\\\"shape shape-4\\\" data-v-03589122></div><div class=\\\"shape shape-5\\\" data-v-03589122></div><div class=\\\"shape shape-6\\\" data-v-03589122></div></div></div>\", 1)), _createCommentVNode(\" Header Section \"), _cache[24] || (_cache[24] = _createStaticVNode(\"<header class=\\\"header-section\\\" data-v-03589122><div class=\\\"header-content\\\" data-v-03589122><div class=\\\"logo-container\\\" data-v-03589122><img class=\\\"logo\\\" src=\\\"\" + _imports_0 + \"\\\" alt=\\\"شعار الفريق\\\" data-v-03589122><div class=\\\"logo-glow\\\" data-v-03589122></div></div><div class=\\\"header-text\\\" data-v-03589122><h2 class=\\\"org-name\\\" data-v-03589122>المجلس الأعلى للشباب</h2><h3 class=\\\"team-name\\\" data-v-03589122>الفريق الوطني للشباب الرقمي</h3></div></div></header>\", 1)), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[22] || (_cache[22] = _createStaticVNode(\"<div class=\\\"card-header\\\" data-v-03589122><div class=\\\"register-icon\\\" data-v-03589122><svg viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" data-v-03589122><path d=\\\"M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" data-v-03589122></path><path d=\\\"M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" data-v-03589122></path></svg></div><h1 class=\\\"card-title\\\" data-v-03589122>إنشاء حساب جديد</h1><p class=\\\"card-subtitle\\\" data-v-03589122>انضم إلى الفريق الوطني للشباب الرقمي</p></div>\", 1)), _createElementVNode(\"form\", {\n    class: \"register-form\",\n    onSubmit: _cache[5] || (_cache[5] = _withModifiers((...args) => $options.doRegister && $options.doRegister(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z\",\n    fill: \"currentColor\"\n  })])], -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    type: \"text\",\n    dir: \"rtl\",\n    placeholder: \"\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.username]]), _cache[7] || (_cache[7] = _createElementVNode(\"label\", {\n    class: \"floating-label\"\n  }, \"اسم المستخدم\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  }), _createElementVNode(\"path\", {\n    d: \"M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  })])], -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.full_name = $event),\n    type: \"text\",\n    dir: \"rtl\",\n    placeholder: \"\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.full_name]]), _cache[9] || (_cache[9] = _createElementVNode(\"label\", {\n    class: \"floating-label\"\n  }, \"الاسم الكامل (الاسم الرباعي واللقب)\", -1 /* CACHED */))])])]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  }), _createElementVNode(\"path\", {\n    d: \"M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  })])], -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.governorate = $event),\n    class: \"form-select\",\n    dir: \"rtl\",\n    required: \"\"\n  }, [_cache[10] || (_cache[10] = _createElementVNode(\"option\", {\n    value: \"\",\n    disabled: \"\"\n  }, \"اختر المحافظة\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.governorates, gov => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: gov,\n      value: gov\n    }, _toDisplayString(gov), 9 /* TEXT, PROPS */, _hoisted_11);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.governorate]]), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n    class: \"select-arrow\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M6 9L12 15L18 9\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  })])], -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z\",\n    fill: \"currentColor\"\n  })])], -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.password = $event),\n    type: \"password\",\n    dir: \"rtl\",\n    placeholder: \"\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.password]]), _cache[14] || (_cache[14] = _createElementVNode(\"label\", {\n    class: \"floating-label\"\n  }, \"كلمة المرور\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"input-icon\"\n  }, [_createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  })])], -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.team_pin = $event),\n    type: \"text\",\n    dir: \"rtl\",\n    placeholder: \"\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.team_pin]]), _cache[16] || (_cache[16] = _createElementVNode(\"label\", {\n    class: \"floating-label\"\n  }, \"رمز الفريق الرقمي\", -1 /* CACHED */))])])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.busy,\n    class: \"register-btn\"\n  }, [!$data.busy ? (_openBlock(), _createElementBlock(\"span\", _hoisted_18, [...(_cache[17] || (_cache[17] = [_createElementVNode(\"span\", {\n    class: \"btn-text\"\n  }, \"إنشاء الحساب\", -1 /* CACHED */), _createElementVNode(\"svg\", {\n    class: \"btn-icon\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M22 12H16L14 15H10L8 12H2\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  }), _createElementVNode(\"path\", {\n    d: \"M5.45 5.11L2 12V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H20C20.5304 20 21.0391 19.7893 21.4142 19.4142C21.7893 19.0391 22 18.5304 22 18V12L18.55 5.11C18.3844 4.77679 18.1292 4.49637 17.813 4.30028C17.4967 4.10419 17.1321 4.0002 16.76 4H7.24C6.86792 4.0002 6.50326 4.10419 6.18704 4.30028C5.87083 4.49637 5.61558 4.77679 5.45 5.11Z\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-linejoin\": \"round\"\n  })], -1 /* CACHED */)]))])) : (_openBlock(), _createElementBlock(\"span\", _hoisted_19, [...(_cache[18] || (_cache[18] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"جاري التسجيل...\", -1 /* CACHED */)]))]))], 8 /* PROPS */, _hoisted_17), $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_cache[19] || (_cache[19] = _createElementVNode(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z\",\n    fill: \"currentColor\"\n  })], -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($data.error), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 32 /* NEED_HYDRATION */), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"p\", _hoisted_22, [_cache[21] || (_cache[21] = _createTextVNode(\" لديك حساب بالفعل؟ \", -1 /* CACHED */)), _createVNode(_component_router_link, {\n    to: \"/login\",\n    class: \"link\"\n  }, {\n    default: _withCtx(() => [...(_cache[20] || (_cache[20] = [_createTextVNode(\"سجّل الدخول\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  })])])])])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onSubmit", "_cache", "_withModifiers", "args", "$options", "doRegister", "_hoisted_4", "_hoisted_5", "_hoisted_6", "viewBox", "fill", "xmlns", "d", "$data", "username", "$event", "type", "dir", "placeholder", "required", "_hoisted_7", "_hoisted_8", "stroke", "full_name", "_hoisted_9", "_hoisted_10", "governorate", "value", "disabled", "_Fragment", "_renderList", "governorates", "gov", "key", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "password", "_hoisted_15", "_hoisted_16", "team_pin", "busy", "_hoisted_18", "_hoisted_19", "error", "_hoisted_20", "_toDisplayString", "_hoisted_21", "_hoisted_22", "_createVNode", "_component_router_link", "to"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\RegisterView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'RegisterView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      full_name: '',\r\n      password: '',\r\n      team_pin: '',\r\n      governorate: '',\r\n      busy: false,\r\n      error: '',\r\n      governorates: [\r\n        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',\r\n        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',\r\n        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    async doRegister() {\r\n      this.error = '';\r\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, full_name: this.full_name, password: this.password, team_pin: this.team_pin, governorate: this.governorate })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\r\n        // persist auth and team pin\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"register-container\">\r\n    <!-- Animated Background -->\r\n    <div class=\"animated-bg\">\r\n      <div class=\"floating-shapes\">\r\n        <div class=\"shape shape-1\"></div>\r\n        <div class=\"shape shape-2\"></div>\r\n        <div class=\"shape shape-3\"></div>\r\n        <div class=\"shape shape-4\"></div>\r\n        <div class=\"shape shape-5\"></div>\r\n        <div class=\"shape shape-6\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Header Section -->\r\n    <header class=\"header-section\">\r\n      <div class=\"header-content\">\r\n        <div class=\"logo-container\">\r\n          <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n          <div class=\"logo-glow\"></div>\r\n        </div>\r\n        <div class=\"header-text\">\r\n          <h2 class=\"org-name\">المجلس الأعلى للشباب</h2>\r\n          <h3 class=\"team-name\">الفريق الوطني للشباب الرقمي</h3>\r\n        </div>\r\n      </div>\r\n    </header>\r\n\r\n    <!-- Main Content -->\r\n    <main class=\"main-content\">\r\n      <div class=\"register-card\">\r\n        <div class=\"card-header\">\r\n          <div class=\"register-icon\">\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n          </div>\r\n          <h1 class=\"card-title\">إنشاء حساب جديد</h1>\r\n          <p class=\"card-subtitle\">انضم إلى الفريق الوطني للشباب الرقمي</p>\r\n        </div>\r\n\r\n        <form class=\"register-form\" @submit.prevent=\"doRegister\">\r\n          <div class=\"form-row\">\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z\" fill=\"currentColor\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"username\"\r\n                  type=\"text\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\">اسم المستخدم</label>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                    <path d=\"M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"full_name\"\r\n                  type=\"text\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\">الاسم الكامل (الاسم الرباعي واللقب)</label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group\">\r\n            <div class=\"select-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  <path d=\"M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </div>\r\n              <select v-model=\"governorate\" class=\"form-select\" dir=\"rtl\" required>\r\n                <option value=\"\" disabled>اختر المحافظة</option>\r\n                <option v-for=\"gov in governorates\" :key=\"gov\" :value=\"gov\">{{ gov }}</option>\r\n              </select>\r\n              <div class=\"select-arrow\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M6 9L12 15L18 9\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z\" fill=\"currentColor\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"password\"\r\n                  type=\"password\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\">كلمة المرور</label>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"team_pin\"\r\n                  type=\"text\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\">رمز الفريق الرقمي</label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <button type=\"submit\" :disabled=\"busy\" class=\"register-btn\">\r\n            <span v-if=\"!busy\" class=\"btn-content\">\r\n              <span class=\"btn-text\">إنشاء الحساب</span>\r\n              <svg class=\"btn-icon\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M22 12H16L14 15H10L8 12H2\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M5.45 5.11L2 12V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H20C20.5304 20 21.0391 19.7893 21.4142 19.4142C21.7893 19.0391 22 18.5304 22 18V12L18.55 5.11C18.3844 4.77679 18.1292 4.49637 17.813 4.30028C17.4967 4.10419 17.1321 4.0002 16.76 4H7.24C6.86792 4.0002 6.50326 4.10419 6.18704 4.30028C5.87083 4.49637 5.61558 4.77679 5.45 5.11Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n            </span>\r\n            <span v-else class=\"btn-loading\">\r\n              <div class=\"loading-spinner\"></div>\r\n              <span>جاري التسجيل...</span>\r\n            </span>\r\n          </button>\r\n\r\n          <div v-if=\"error\" class=\"error-message\">\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z\" fill=\"currentColor\"/>\r\n            </svg>\r\n            <span>{{ error }}</span>\r\n          </div>\r\n        </form>\r\n\r\n        <div class=\"card-footer\">\r\n          <p class=\"login-link\">\r\n            لديك حساب بالفعل؟\r\n            <router-link to=\"/login\" class=\"link\">سجّل الدخول</router-link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n/* Global Styles */\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.register-container {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\r\n  direction: rtl;\r\n}\r\n\r\n/* Animated Background */\r\n.animated-bg {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.floating-shapes {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shape {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  background: linear-gradient(45deg, rgba(79, 70, 229, 0.08), rgba(124, 58, 237, 0.08));\r\n  animation: float 8s ease-in-out infinite;\r\n}\r\n\r\n.shape-1 {\r\n  width: 100px;\r\n  height: 100px;\r\n  top: 15%;\r\n  left: 8%;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.shape-2 {\r\n  width: 140px;\r\n  height: 140px;\r\n  top: 25%;\r\n  right: 12%;\r\n  animation-delay: 2.5s;\r\n}\r\n\r\n.shape-3 {\r\n  width: 80px;\r\n  height: 80px;\r\n  bottom: 35%;\r\n  left: 15%;\r\n  animation-delay: 5s;\r\n}\r\n\r\n.shape-4 {\r\n  width: 120px;\r\n  height: 120px;\r\n  bottom: 15%;\r\n  right: 8%;\r\n  animation-delay: 1.5s;\r\n}\r\n\r\n.shape-5 {\r\n  width: 160px;\r\n  height: 160px;\r\n  top: 45%;\r\n  left: 45%;\r\n  transform: translate(-50%, -50%);\r\n  animation-delay: 3.5s;\r\n}\r\n\r\n.shape-6 {\r\n  width: 90px;\r\n  height: 90px;\r\n  top: 8%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  animation-delay: 4.5s;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px) rotate(0deg);\r\n    opacity: 0.4;\r\n  }\r\n  50% {\r\n    transform: translateY(-25px) rotate(180deg);\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n/* Header Section */\r\n.header-section {\r\n  position: relative;\r\n  z-index: 2;\r\n  padding: 1.5rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  backdrop-filter: blur(10px);\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 2rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.logo-container {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.logo {\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(79, 70, 229, 0.5);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.logo-glow {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 50%;\r\n  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    transform: translate(-50%, -50%) scale(1);\r\n    opacity: 0.5;\r\n  }\r\n  50% {\r\n    transform: translate(-50%, -50%) scale(1.1);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n.header-text {\r\n  text-align: center;\r\n}\r\n\r\n.org-name {\r\n  font-size: clamp(1.2rem, 4vw, 1.8rem);\r\n  font-weight: 800;\r\n  color: #f8fafc;\r\n  margin: 0 0 0.5rem 0;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.team-name {\r\n  font-size: clamp(1rem, 3vw, 1.4rem);\r\n  font-weight: 600;\r\n  color: #cbd5e1;\r\n  margin: 0;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n  position: relative;\r\n  z-index: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: calc(100vh - 140px);\r\n  padding: 2rem 1rem;\r\n}\r\n\r\n.register-card {\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.15);\r\n  border-radius: 24px;\r\n  padding: 2.5rem;\r\n  width: 100%;\r\n  max-width: 600px;\r\n  box-shadow:\r\n    0 20px 40px rgba(0, 0, 0, 0.4),\r\n    0 0 0 1px rgba(255, 255, 255, 0.05);\r\n  transition: all 0.4s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.register-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);\r\n}\r\n\r\n.register-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow:\r\n    0 32px 64px rgba(0, 0, 0, 0.5),\r\n    0 0 0 1px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Card Header */\r\n.card-header {\r\n  text-align: center;\r\n  margin-bottom: 2.5rem;\r\n}\r\n\r\n.register-icon {\r\n  width: 64px;\r\n  height: 64px;\r\n  margin: 0 auto 1.5rem;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);\r\n  animation: iconFloat 3s ease-in-out infinite;\r\n}\r\n\r\n.register-icon svg {\r\n  width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n@keyframes iconFloat {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n.card-title {\r\n  font-size: 2rem;\r\n  font-weight: 800;\r\n  margin: 0 0 0.5rem 0;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.card-subtitle {\r\n  color: #94a3b8;\r\n  font-size: 1rem;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Form Styles */\r\n.register-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.form-row {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 1rem;\r\n}\r\n\r\n.input-group {\r\n  position: relative;\r\n}\r\n\r\n.input-wrapper, .select-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.input-icon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  color: #64748b;\r\n  z-index: 2;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.form-input, .form-select {\r\n  width: 100%;\r\n  padding: 1rem 3rem 1rem 1rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 16px;\r\n  color: #f1f5f9;\r\n  font-size: 1rem;\r\n  transition: all 0.3s ease;\r\n  text-align: right;\r\n  direction: rtl;\r\n}\r\n\r\n.form-select {\r\n  cursor: pointer;\r\n  appearance: none;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n}\r\n\r\n.form-input:focus, .form-select:focus {\r\n  outline: none;\r\n  border-color: #10b981;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);\r\n}\r\n\r\n.form-input:focus + .floating-label,\r\n.form-input:not(:placeholder-shown) + .floating-label,\r\n.form-select:focus + .floating-label,\r\n.form-select:not([value=\"\"]) + .floating-label {\r\n  transform: translateY(-2.5rem) scale(0.85);\r\n  color: #10b981;\r\n  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);\r\n\r\n}\r\n\r\n.form-input:focus ~ .input-icon,\r\n.form-select:focus ~ .input-icon {\r\n  color: #10b981;\r\n}\r\n\r\n.floating-label {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #64748b;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  pointer-events: none;\r\n  padding: 0 0.5rem;\r\n}\r\n\r\n.select-label {\r\n  z-index: 1;\r\n}\r\n\r\n.select-arrow {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  color: #64748b;\r\n  pointer-events: none;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.form-select:focus ~ .select-arrow {\r\n  color: #10b981;\r\n}\r\n\r\n.form-select option {\r\n  background: #1b1f24;\r\n  color: #e5e7eb;\r\n  padding: 0.5rem;\r\n}\r\n\r\n/* Button Styles */\r\n.register-btn {\r\n  width: 100%;\r\n  padding: 1rem 2rem;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  border: none;\r\n  border-radius: 16px;\r\n  color: white;\r\n  font-size: 1.1rem;\r\n  font-weight: 700;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);\r\n  margin-top: 1rem;\r\n}\r\n\r\n.register-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.register-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.register-btn:hover {\r\n  background: linear-gradient(135deg, #34d399, #10b981);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.4);\r\n}\r\n\r\n.register-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.register-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.btn-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.btn-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.register-btn:hover .btn-icon {\r\n  transform: translateX(-4px);\r\n}\r\n\r\n.btn-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-top: 2px solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Error Message */\r\n.error-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem;\r\n  background: rgba(239, 68, 68, 0.1);\r\n  border: 1px solid rgba(239, 68, 68, 0.3);\r\n  border-radius: 12px;\r\n  color: #fca5a5;\r\n  font-size: 0.9rem;\r\n  margin-top: 1rem;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n.error-message svg {\r\n  width: 20px;\r\n  height: 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Card Footer */\r\n.card-footer {\r\n  margin-top: 2rem;\r\n  text-align: center;\r\n}\r\n\r\n.login-link {\r\n  color: #94a3b8;\r\n  font-size: 0.95rem;\r\n  margin: 0;\r\n}\r\n\r\n.link {\r\n  color: #10b981;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.link::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: 0;\r\n  width: 0;\r\n  height: 2px;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.link:hover::after {\r\n  width: 100%;\r\n}\r\n\r\n.link:hover {\r\n  color: #34d399;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .header-section {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1.5rem 1rem;\r\n    min-height: calc(100vh - 120px);\r\n  }\r\n\r\n  .register-card {\r\n    padding: 2rem;\r\n    border-radius: 20px;\r\n    max-width: 500px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .register-icon {\r\n    width: 56px;\r\n    height: 56px;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .register-icon svg {\r\n    width: 28px;\r\n    height: 28px;\r\n  }\r\n\r\n  .form-row {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .form-input, .form-select {\r\n    padding: 0.875rem 2.5rem 0.875rem 0.875rem;\r\n    font-size: 16px; /* Prevents zoom on iOS */\r\n  }\r\n\r\n  .register-btn {\r\n    padding: 0.875rem 1.5rem;\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .header-section {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 70px;\r\n    height: 70px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1rem 0.75rem;\r\n  }\r\n\r\n  .register-card {\r\n    padding: 1.5rem;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .card-subtitle {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .register-icon {\r\n    width: 48px;\r\n    height: 48px;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .register-icon svg {\r\n    width: 24px;\r\n    height: 24px;\r\n  }\r\n\r\n  .register-form {\r\n    gap: 1.25rem;\r\n  }\r\n\r\n  .form-input, .form-select {\r\n    padding: 0.75rem 2.25rem 0.75rem 0.75rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .input-icon {\r\n    left: 0.75rem;\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .floating-label {\r\n    right: 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .select-arrow {\r\n    right: 0.75rem;\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .register-btn {\r\n    padding: 0.75rem 1.25rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .btn-icon {\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .error-message {\r\n    padding: 0.75rem;\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .login-link {\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 360px) {\r\n  .register-card {\r\n    padding: 1.25rem;\r\n  }\r\n\r\n  .card-header {\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.375rem;\r\n  }\r\n\r\n  .form-input, .form-select {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .floating-label {\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n</style>"], "mappings": ";OAoE4BA,UAA6B;;EAjBlDC,KAAK,EAAC;AAAoB;;EA4BvBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAe;;EAajBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;EAkBvBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;EAoBzBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAgB;;;EAmBxBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;EAkBvBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;;;EAoBTA,KAAK,EAAC;;;;EAOZA,KAAK,EAAC;;;;EAMHA,KAAK,EAAC;;;EAQrBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAY;;;uBAtK7BC,mBAAA,CA6KM,OA7KNC,UA6KM,GA5KJC,mBAAA,yBAA4B,E,gdAY5BA,mBAAA,oBAAuB,E,qhBAcvBA,mBAAA,kBAAqB,EACrBC,mBAAA,CAgJO,QAhJPC,UAgJO,GA/ILD,mBAAA,CA8IM,OA9INE,UA8IM,G,+zBAlIJF,mBAAA,CA0HO;IA1HDJ,KAAK,EAAC,eAAe;IAAEO,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,IAAAF,IAAA,CAAU;MACrDN,mBAAA,CAuCM,OAvCNS,UAuCM,GAtCJT,mBAAA,CAiBM,OAjBNU,UAiBM,GAhBJV,mBAAA,CAeM,OAfNW,UAeM,G,0BAdJX,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAEM;IAFDY,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCd,mBAAA,CAAwL;IAAlLe,CAAC,EAAC,0JAA0J;IAACF,IAAI,EAAC;4CAG5Kb,mBAAA,CAOE;+DANSgB,KAAA,CAAAC,QAAQ,GAAAC,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXC,GAAG,EAAC,KAAK;IACTC,WAAW,EAAC,EAAE;IACdzB,KAAK,EAAC,YAAY;IAClB0B,QAAQ,EAAR;iDALSN,KAAA,CAAAC,QAAQ,E,6BAOnBjB,mBAAA,CAAkD;IAA3CJ,KAAK,EAAC;EAAgB,GAAC,cAAY,oB,KAI9CI,mBAAA,CAkBM,OAlBNuB,UAkBM,GAjBJvB,mBAAA,CAgBM,OAhBNwB,UAgBM,G,0BAfJxB,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDY,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCd,mBAAA,CAAsQ;IAAhQe,CAAC,EAAC,sKAAsK;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;MAC7PzB,mBAAA,CAAiN;IAA3Me,CAAC,EAAC,iHAAiH;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;4CAG5MzB,mBAAA,CAOE;+DANSgB,KAAA,CAAAU,SAAS,GAAAR,MAAA;IAClBC,IAAI,EAAC,MAAM;IACXC,GAAG,EAAC,KAAK;IACTC,WAAW,EAAC,EAAE;IACdzB,KAAK,EAAC,YAAY;IAClB0B,QAAQ,EAAR;iDALSN,KAAA,CAAAU,SAAS,E,6BAOpB1B,mBAAA,CAAyE;IAAlEJ,KAAK,EAAC;EAAgB,GAAC,qCAAmC,oB,OAKvEI,mBAAA,CAkBM,OAlBN2B,UAkBM,GAjBJ3B,mBAAA,CAgBM,OAhBN4B,WAgBM,G,4BAfJ5B,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDY,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCd,mBAAA,CAAyR;IAAnRe,CAAC,EAAC,yLAAyL;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;MAChRzB,mBAAA,CAAmN;IAA7Me,CAAC,EAAC,mHAAmH;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;4CAG9MzB,mBAAA,CAGS;+DAHQgB,KAAA,CAAAa,WAAW,GAAAX,MAAA;IAAEtB,KAAK,EAAC,aAAa;IAACwB,GAAG,EAAC,KAAK;IAACE,QAAQ,EAAR;kCAC1DtB,mBAAA,CAAgD;IAAxC8B,KAAK,EAAC,EAAE;IAACC,QAAQ,EAAR;KAAS,eAAa,sB,kBACvClC,mBAAA,CAA8EmC,SAAA,QAAAC,WAAA,CAAxDjB,KAAA,CAAAkB,YAAY,EAAnBC,GAAG;yBAAlBtC,mBAAA,CAA8E;MAAzCuC,GAAG,EAAED,GAAG;MAAGL,KAAK,EAAEK;wBAAQA,GAAG,wBAAAE,WAAA;2EAFnDrB,KAAA,CAAAa,WAAW,E,+BAI5B7B,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAc,IACvBI,mBAAA,CAEM;IAFDY,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCd,mBAAA,CAAiH;IAA3Ge,CAAC,EAAC,iBAAiB;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;gCAMhHzB,mBAAA,CAsCM,OAtCNsC,WAsCM,GArCJtC,mBAAA,CAiBM,OAjBNuC,WAiBM,GAhBJvC,mBAAA,CAeM,OAfNwC,WAeM,G,4BAdJxC,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAEM;IAFDY,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCd,mBAAA,CAA0S;IAApSe,CAAC,EAAC,4QAA4Q;IAACF,IAAI,EAAC;4CAG9Rb,mBAAA,CAOE;+DANSgB,KAAA,CAAAyB,QAAQ,GAAAvB,MAAA;IACjBC,IAAI,EAAC,UAAU;IACfC,GAAG,EAAC,KAAK;IACTC,WAAW,EAAC,EAAE;IACdzB,KAAK,EAAC,YAAY;IAClB0B,QAAQ,EAAR;iDALSN,KAAA,CAAAyB,QAAQ,E,+BAOnBzC,mBAAA,CAAiD;IAA1CJ,KAAK,EAAC;EAAgB,GAAC,aAAW,oB,KAI7CI,mBAAA,CAiBM,OAjBN0C,WAiBM,GAhBJ1C,mBAAA,CAeM,OAfN2C,WAeM,G,4BAdJ3C,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAEM;IAFDY,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCd,mBAAA,CAA8J;IAAxJe,CAAC,EAAC,8DAA8D;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;4CAGzJzB,mBAAA,CAOE;+DANSgB,KAAA,CAAA4B,QAAQ,GAAA1B,MAAA;IACjBC,IAAI,EAAC,MAAM;IACXC,GAAG,EAAC,KAAK;IACTC,WAAW,EAAC,EAAE;IACdzB,KAAK,EAAC,YAAY;IAClB0B,QAAQ,EAAR;iDALSN,KAAA,CAAA4B,QAAQ,E,+BAOnB5C,mBAAA,CAAuD;IAAhDJ,KAAK,EAAC;EAAgB,GAAC,mBAAiB,oB,OAKrDI,mBAAA,CAYS;IAZDmB,IAAI,EAAC,QAAQ;IAAEY,QAAQ,EAAEf,KAAA,CAAA6B,IAAI;IAAEjD,KAAK,EAAC;OAC9BoB,KAAA,CAAA6B,IAAI,I,cAAjBhD,mBAAA,CAMO,QANPiD,WAMO,OAAA1C,MAAA,SAAAA,MAAA,QALLJ,mBAAA,CAA0C;IAApCJ,KAAK,EAAC;EAAU,GAAC,cAAY,oBACnCI,mBAAA,CAGM;IAHDJ,KAAK,EAAC,UAAU;IAACgB,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MAC1Dd,mBAAA,CAA2H;IAArHe,CAAC,EAAC,2BAA2B;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;MAClHzB,mBAAA,CAAgd;IAA1ce,CAAC,EAAC,gXAAgX;IAACU,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC,GAAG;IAAC,gBAAc,EAAC,OAAO;IAAC,iBAAe,EAAC;+CAG3c5B,mBAAA,CAGO,QAHPkD,WAGO,OAAA3C,MAAA,SAAAA,MAAA,QAFLJ,mBAAA,CAAmC;IAA9BJ,KAAK,EAAC;EAAiB,2BAC5BI,mBAAA,CAA4B,cAAtB,iBAAe,mB,qCAIdgB,KAAA,CAAAgC,KAAK,I,cAAhBnD,mBAAA,CAKM,OALNoD,WAKM,G,4BAJJjD,mBAAA,CAEM;IAFDY,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC;MACzCd,mBAAA,CAAwI;IAAlIe,CAAC,EAAC,0GAA0G;IAACF,IAAI,EAAC;0BAE1Hb,mBAAA,CAAwB,cAAAkD,gBAAA,CAAflC,KAAA,CAAAgC,KAAK,iB,mEAIlBhD,mBAAA,CAKM,OALNmD,WAKM,GAJJnD,mBAAA,CAGI,KAHJoD,WAGI,G,6CAHkB,qBAEpB,qBAAAC,YAAA,CAA+DC,sBAAA;IAAlDC,EAAE,EAAC,QAAQ;IAAC3D,KAAK,EAAC;;sBAAO,MAAW,KAAAQ,MAAA,SAAAA,MAAA,Q,iBAAX,aAAW,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}