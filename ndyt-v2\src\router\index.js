import ActivityView from "@/views/ActivityView.vue";
import LoginView from "@/views/LoginView.vue";
import RegisterView from "@/views/RegisterView.vue";
import AdminView from "@/views/AdminView.vue";
import { createRouter, createWebHistory } from "vue-router";
import Swal from 'sweetalert2';

const router = createRouter({
  // The app is served under this base path by Express
  history: createWebHistory('/ndyt/'),
  // Disable router caching
  scrollBehavior() {
    return { top: 0 }
  },
  routes: [
    {
      path: '/',
      component: ActivityView,
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      component: LoginView,
      meta: { guestOnly: true }
    },
    {
      path: '/register',
      component: RegisterView,
      meta: { guestOnly: true }
    },
    {
      path: '/admin',
      component: AdminView,
      meta: { requiresAuth: true, requiresAdmin: true }
    },
  ]
});

router.beforeEach(async (to, _, next) => {
  const token = localStorage.getItem('ndyt_token');
  
  // Force cache busting for login route
  if (to.path === '/login') {
    // Add timestamp to force fresh load
    const timestamp = Date.now();
    if (!to.query.t || to.query.t !== timestamp.toString()) {
      next({ 
        path: '/login', 
        query: { ...to.query, t: timestamp },
        replace: true 
      });
      return;
    }
  }
  
  if (to.meta?.requiresAuth && !token) {
    const timestamp = Date.now();
    next({ 
      path: '/login', 
      query: { redirect: to.fullPath, t: timestamp }
    });
    return;
  }
  
  if (to.meta?.guestOnly && token) {
    next('/');
    return;
  }
  
  // Check admin access for admin routes
  if (to.meta?.requiresAdmin && token) {
    try {
      // Import API service dynamically to avoid circular dependency
      const { default: apiService } = await import('@/services/api');
      const response = await apiService.get('/auth/me');

      if (response.ok) {
        const user = await response.json();
        if (user.rank !== 'admin' && user.rank !== 'super_admin') {
          Swal.fire({
            title: 'غير مصرح',
            text: 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
            icon: 'error',
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'موافق'
          });
          next('/');
          return;
        }
      } else {
        next('/login');
        return;
      }
    } catch (error) {
      if (error.message === 'Session expired') {
        // Token expiration is already handled by the API service
        return;
      }
      console.error('Error checking admin access:', error);
      next('/login');
      return;
    }
  }
  
  next();
});

export default router;
