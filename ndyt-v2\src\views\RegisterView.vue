<script>
export default {
  name: 'RegisterView',
  data() {
    return {
      username: '',
      full_name: '',
      password: '',
      team_pin: '',
      governorate: '',
      busy: false,
      error: '',
      governorates: [
        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',
        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',
        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'
      ]
    };
  },
  methods: {
    async doRegister() {
      this.error = '';
      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {
        this.error = 'يرجى تعبئة جميع الحقول';
        return;
      }
      this.busy = true;
      try {
        const res = await fetch('/api/v1/ndyt-activities/auth/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username: this.username, full_name: this.full_name, password: this.password, team_pin: this.team_pin, governorate: this.governorate })
        });
        const data = await res.json();
        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');
        // persist auth and team pin
        localStorage.setItem('ndyt_token', data.token);
        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));
        localStorage.setItem('ndyt_team_pin', this.team_pin);
        const redirect = this.$route.query.redirect || '/';
        this.$router.replace(redirect);
      } catch (e) {
        this.error = e.message;
      } finally {
        this.busy = false;
      }
    }
  }
}
</script>
<template>
  <div class="register-container">
    <!-- Animated Background -->
    <div class="animated-bg">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
        <div class="shape shape-6"></div>
      </div>
    </div>

    <!-- Header Section -->
    <header class="header-section">
      <div class="header-content">
        <div class="logo-container">
          <img class="logo" src="../assets/ndyt_logo.jpg" alt="شعار الفريق">
          <div class="logo-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="org-name">المجلس الأعلى للشباب</h2>
          <h3 class="team-name">الفريق الوطني للشباب الرقمي</h3>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="register-card">
        <div class="card-header">
          <div class="register-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h1 class="card-title">إنشاء حساب جديد</h1>
          <p class="card-subtitle">انضم إلى الفريق الوطني للشباب الرقمي</p>
        </div>

        <form class="register-form" @submit.prevent="doRegister">
          <div class="form-row">
            <div class="input-group">
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/>
                  </svg>
                </div>
                <input
                  v-model="username"
                  type="text"
                  dir="rtl"
                  placeholder=""
                  class="form-input"
                  required
                />
                <label class="floating-label">اسم المستخدم</label>
              </div>
            </div>

            <div class="input-group">
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <input
                  v-model="full_name"
                  type="text"
                  dir="rtl"
                  placeholder=""
                  class="form-input"
                  required
                />
                <label class="floating-label">الاسم الرباعي واللقب</label>
              </div>
            </div>
          </div>

          <div class="input-group">
            <div class="select-wrapper">
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <select v-model="governorate" class="form-select" dir="rtl" required>
                <option value="" disabled>اختر المحافظة</option>
                <option v-for="gov in governorates" :key="gov" :value="gov">{{ gov }}</option>
              </select>
              <div class="select-arrow">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="input-group">
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z" fill="currentColor"/>
                  </svg>
                </div>
                <input
                  v-model="password"
                  type="password"
                  dir="rtl"
                  placeholder=""
                  class="form-input"
                  required
                />
                <label class="floating-label">كلمة المرور</label>
              </div>
            </div>

            <div class="input-group">
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <input
                  v-model="team_pin"
                  type="password"
                  dir="rtl"
                  placeholder=""
                  class="form-input"
                  required
                />
                <label class="floating-label">رمز الفريق الرقمي</label>
              </div>
            </div>
          </div>

          <button type="submit" :disabled="busy" class="register-btn">
            <span v-if="!busy" class="btn-content">
              <span class="btn-text">إنشاء الحساب</span>
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 12H16L14 15H10L8 12H2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M5.45 5.11L2 12V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H20C20.5304 20 21.0391 19.7893 21.4142 19.4142C21.7893 19.0391 22 18.5304 22 18V12L18.55 5.11C18.3844 4.77679 18.1292 4.49637 17.813 4.30028C17.4967 4.10419 17.1321 4.0002 16.76 4H7.24C6.86792 4.0002 6.50326 4.10419 6.18704 4.30028C5.87083 4.49637 5.61558 4.77679 5.45 5.11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span v-else class="btn-loading">
              <div class="loading-spinner"></div>
              <span>جاري التسجيل...</span>
            </span>
          </button>

          <div v-if="error" class="error-message">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor"/>
            </svg>
            <span>{{ error }}</span>
          </div>
        </form>

        <div class="card-footer">
          <p class="login-link">
            لديك حساب بالفعل؟
            <router-link to="/login" class="link">سجّل الدخول</router-link>
          </p>
        </div>
      </div>
    </main>
  </div>
</template>
<style scoped>
/* Global Styles */
* {
  box-sizing: border-box;
}

.register-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  direction: rtl;
}

/* Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.08), rgba(124, 58, 237, 0.08));
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 15%;
  left: 8%;
  animation-delay: 0s;
}

.shape-2 {
  width: 140px;
  height: 140px;
  top: 25%;
  right: 12%;
  animation-delay: 2.5s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 35%;
  left: 15%;
  animation-delay: 5s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  bottom: 15%;
  right: 8%;
  animation-delay: 1.5s;
}

.shape-5 {
  width: 160px;
  height: 160px;
  top: 45%;
  left: 45%;
  transform: translate(-50%, -50%);
  animation-delay: 3.5s;
}

.shape-6 {
  width: 90px;
  height: 90px;
  top: 8%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
    opacity: 0.7;
  }
}

/* Header Section */
.header-section {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 3px solid rgba(79, 70, 229, 0.5);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
}

.header-text {
  text-align: center;
}

.org-name {
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  font-weight: 800;
  color: #f8fafc;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.team-name {
  font-size: clamp(1rem, 3vw, 1.4rem);
  font-weight: 600;
  color: #cbd5e1;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 140px);
  padding: 2rem 1rem;
}

.register-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 24px;
  padding: 2.5rem;
  width: 100%;
  max-width: 600px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.register-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);
}

.register-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Card Header */
.card-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.register-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  animation: iconFloat 3s ease-in-out infinite;
}

.register-icon svg {
  width: 32px;
  height: 32px;
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

.card-title {
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-subtitle {
  color: #94a3b8;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* Form Styles */
.register-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.input-group {
  position: relative;
}

.input-wrapper, .select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #64748b;
  z-index: 2;
  transition: color 0.3s ease;
}

.form-input, .form-select {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: #f1f5f9;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
}

.form-select {
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #10b981;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.form-input:focus + .floating-label,
.form-input:not(:placeholder-shown) + .floating-label,
.form-select:focus + .floating-label,
.form-select:not([value=""]) + .floating-label {
  transform: translateY(-2.5rem) scale(0.85);
  color: #10b981;
  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);

}

.form-input:focus ~ .input-icon,
.form-select:focus ~ .input-icon {
  color: #10b981;
}

.floating-label {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  pointer-events: none;
  padding: 0 0.5rem;
}

.select-label {
  z-index: 1;
}

.select-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #64748b;
  pointer-events: none;
  transition: color 0.3s ease;
}

.form-select:focus ~ .select-arrow {
  color: #10b981;
}

.form-select option {
  background: #1b1f24;
  color: #e5e7eb;
  padding: 0.5rem;
}

/* Button Styles */
.register-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  margin-top: 1rem;
}

.register-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.register-btn:hover::before {
  left: 100%;
}

.register-btn:hover {
  background: linear-gradient(135deg, #34d399, #10b981);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.4);
}

.register-btn:active {
  transform: translateY(0);
}

.register-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.register-btn:hover .btn-icon {
  transform: translateX(-4px);
}

.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  color: #fca5a5;
  font-size: 0.9rem;
  margin-top: 1rem;
  animation: slideIn 0.3s ease;
}

.error-message svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Footer */
.card-footer {
  margin-top: 2rem;
  text-align: center;
}

.login-link {
  color: #94a3b8;
  font-size: 0.95rem;
  margin: 0;
}

.link {
  color: #10b981;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}

.link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #10b981, #059669);
  transition: width 0.3s ease;
}

.link:hover::after {
  width: 100%;
}

.link:hover {
  color: #34d399;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .logo {
    width: 60px;
    height: 60px;
  }

  .logo-glow {
    width: 80px;
    height: 80px;
  }

  .main-content {
    padding: 1.5rem 1rem;
    min-height: calc(100vh - 120px);
  }

  .register-card {
    padding: 2rem;
    border-radius: 20px;
    max-width: 500px;
  }

  .card-title {
    font-size: 1.75rem;
  }

  .register-icon {
    width: 56px;
    height: 56px;
    margin-bottom: 1rem;
  }

  .register-icon svg {
    width: 28px;
    height: 28px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .form-input, .form-select {
    padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .register-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .header-section {
    padding: 0.75rem;
  }

  .logo {
    width: 50px;
    height: 50px;
  }

  .logo-glow {
    width: 70px;
    height: 70px;
  }

  .main-content {
    padding: 1rem 0.75rem;
  }

  .register-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .card-title {
    font-size: 1.5rem;
  }

  .card-subtitle {
    font-size: 0.9rem;
  }

  .register-icon {
    width: 48px;
    height: 48px;
    border-radius: 16px;
  }

  .register-icon svg {
    width: 24px;
    height: 24px;
  }

  .register-form {
    gap: 1.25rem;
  }

  .form-input, .form-select {
    padding: 0.75rem 2.25rem 0.75rem 0.75rem;
    border-radius: 12px;
  }

  .input-icon {
    left: 0.75rem;
    width: 18px;
    height: 18px;
  }

  .floating-label {
    right: 0.75rem;
    font-size: 0.9rem;
  }

  .select-arrow {
    right: 0.75rem;
    width: 18px;
    height: 18px;
  }

  .register-btn {
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
  }

  .btn-icon {
    width: 18px;
    height: 18px;
  }

  .error-message {
    padding: 0.75rem;
    font-size: 0.85rem;
  }

  .login-link {
    font-size: 0.9rem;
  }
}

@media (max-width: 360px) {
  .register-card {
    padding: 1.25rem;
  }

  .card-header {
    margin-bottom: 2rem;
  }

  .card-title {
    font-size: 1.375rem;
  }

  .form-input, .form-select {
    font-size: 14px;
  }

  .floating-label {
    font-size: 0.85rem;
  }
}
</style>