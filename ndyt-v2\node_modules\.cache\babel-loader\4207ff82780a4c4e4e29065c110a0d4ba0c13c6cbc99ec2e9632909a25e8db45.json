{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport router from '@/router';\nclass ApiService {\n  constructor() {\n    this.baseURL = '/api/v1/ndyt-activities';\n  }\n  async request(endpoint, options = {}) {\n    const token = localStorage.getItem('ndyt_token');\n    const teamPin = localStorage.getItem('ndyt_team_pin');\n    const config = {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    };\n\n    // Add authorization header if token exists\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Add team pin header if it exists\n    if (teamPin && (config.method === 'POST' || config.method === 'PUT' || config.method === 'DELETE')) {\n      config.headers['x-team-pin'] = teamPin;\n    }\n    const response = await fetch(`${this.baseURL}${endpoint}`, config);\n\n    // Handle token expiration globally\n    if (response.status === 401 || response.status === 403) {\n      this.handleTokenExpiration();\n      throw new Error('Session expired');\n    }\n    return response;\n  }\n  handleTokenExpiration() {\n    // Clear all authentication data\n    localStorage.removeItem('ndyt_token');\n    localStorage.removeItem('ndyt_user');\n    localStorage.removeItem('ndyt_team_pin');\n\n    // Show toast notification\n    try {\n      // Try to use Vue's toast if available\n      const app = document.getElementById('app')?.__vue_app__;\n      if (app && app.config.globalProperties.$toast) {\n        app.config.globalProperties.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');\n      } else {\n        // Fallback to console log\n        console.warn('Session expired. Redirecting to login.');\n      }\n    } catch (error) {\n      console.warn('Session expired. Redirecting to login.');\n    }\n\n    // Redirect to login page\n    router.push('/login');\n  }\n\n  // Convenience methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'GET'\n    });\n  }\n  async post(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'POST',\n      body: JSON.stringify(data)\n    });\n  }\n  async put(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'PUT',\n      body: JSON.stringify(data)\n    });\n  }\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'DELETE'\n    });\n  }\n\n  // File upload method\n  async uploadFile(file, options = {}) {\n    const token = localStorage.getItem('ndyt_token');\n    const formData = new FormData();\n    formData.append('file', file);\n    const config = {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`\n      },\n      body: formData,\n      ...options\n    };\n    const response = await fetch(`${this.baseURL}/upload-file`, config);\n    if (response.status === 401 || response.status === 403) {\n      this.handleTokenExpiration();\n      throw new Error('Session expired');\n    }\n    return response;\n  }\n}\nexport default new ApiService();", "map": {"version": 3, "names": ["router", "ApiService", "constructor", "baseURL", "request", "endpoint", "options", "token", "localStorage", "getItem", "teamPin", "config", "method", "headers", "response", "fetch", "status", "handleTokenExpiration", "Error", "removeItem", "app", "document", "getElementById", "__vue_app__", "globalProperties", "$toast", "error", "console", "warn", "push", "get", "post", "data", "body", "JSON", "stringify", "put", "delete", "uploadFile", "file", "formData", "FormData", "append"], "sources": ["F:/My Apps/iqtp/iqtp_backend/ndyt-v2/src/services/api.js"], "sourcesContent": ["import router from '@/router'\n\nclass ApiService {\n  constructor() {\n    this.baseURL = '/api/v1/ndyt-activities'\n  }\n\n  async request(endpoint, options = {}) {\n    const token = localStorage.getItem('ndyt_token')\n    const teamPin = localStorage.getItem('ndyt_team_pin')\n    \n    const config = {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    }\n\n    // Add authorization header if token exists\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`\n    }\n\n    // Add team pin header if it exists\n    if (teamPin && (config.method === 'POST' || config.method === 'PUT' || config.method === 'DELETE')) {\n      config.headers['x-team-pin'] = teamPin\n    }\n\n    const response = await fetch(`${this.baseURL}${endpoint}`, config)\n\n    // Handle token expiration globally\n    if (response.status === 401 || response.status === 403) {\n      this.handleTokenExpiration()\n      throw new Error('Session expired')\n    }\n\n    return response\n  }\n\n  handleTokenExpiration() {\n    // Clear all authentication data\n    localStorage.removeItem('ndyt_token')\n    localStorage.removeItem('ndyt_user')\n    localStorage.removeItem('ndyt_team_pin')\n\n    // Show toast notification\n    try {\n      // Try to use Vue's toast if available\n      const app = document.getElementById('app')?.__vue_app__\n      if (app && app.config.globalProperties.$toast) {\n        app.config.globalProperties.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى')\n      } else {\n        // Fallback to console log\n        console.warn('Session expired. Redirecting to login.')\n      }\n    } catch (error) {\n      console.warn('Session expired. Redirecting to login.')\n    }\n\n    // Redirect to login page\n    router.push('/login')\n  }\n\n  // Convenience methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, { ...options, method: 'GET' })\n  }\n\n  async post(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'POST',\n      body: JSON.stringify(data)\n    })\n  }\n\n  async put(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'PUT',\n      body: JSON.stringify(data)\n    })\n  }\n\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, { ...options, method: 'DELETE' })\n  }\n\n  // File upload method\n  async uploadFile(file, options = {}) {\n    const token = localStorage.getItem('ndyt_token')\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const config = {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`\n      },\n      body: formData,\n      ...options\n    }\n\n    const response = await fetch(`${this.baseURL}/upload-file`, config)\n\n    if (response.status === 401 || response.status === 403) {\n      this.handleTokenExpiration()\n      throw new Error('Session expired')\n    }\n\n    return response\n  }\n}\n\nexport default new ApiService()\n"], "mappings": ";AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,yBAAyB;EAC1C;EAEA,MAAMC,OAAOA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAErD,MAAME,MAAM,GAAG;MACbC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGP,OAAO,CAACO;MACb,CAAC;MACD,GAAGP;IACL,CAAC;;IAED;IACA,IAAIC,KAAK,EAAE;MACTI,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACrD;;IAEA;IACA,IAAIG,OAAO,KAAKC,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,KAAK,IAAID,MAAM,CAACC,MAAM,KAAK,QAAQ,CAAC,EAAE;MAClGD,MAAM,CAACE,OAAO,CAAC,YAAY,CAAC,GAAGH,OAAO;IACxC;IAEA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACZ,OAAO,GAAGE,QAAQ,EAAE,EAAEM,MAAM,CAAC;;IAElE;IACA,IAAIG,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACtD,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;IACpC;IAEA,OAAOJ,QAAQ;EACjB;EAEAG,qBAAqBA,CAAA,EAAG;IACtB;IACAT,YAAY,CAACW,UAAU,CAAC,YAAY,CAAC;IACrCX,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;IACpCX,YAAY,CAACW,UAAU,CAAC,eAAe,CAAC;;IAExC;IACA,IAAI;MACF;MACA,MAAMC,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAAC,KAAK,CAAC,EAAEC,WAAW;MACvD,IAAIH,GAAG,IAAIA,GAAG,CAACT,MAAM,CAACa,gBAAgB,CAACC,MAAM,EAAE;QAC7CL,GAAG,CAACT,MAAM,CAACa,gBAAgB,CAACC,MAAM,CAACC,KAAK,CAAC,iDAAiD,CAAC;MAC7F,CAAC,MAAM;QACL;QACAC,OAAO,CAACC,IAAI,CAAC,wCAAwC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,wCAAwC,CAAC;IACxD;;IAEA;IACA5B,MAAM,CAAC6B,IAAI,CAAC,QAAQ,CAAC;EACvB;;EAEA;EACA,MAAMC,GAAGA,CAACzB,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAChC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAE,GAAGC,OAAO;MAAEM,MAAM,EAAE;IAAM,CAAC,CAAC;EAC9D;EAEA,MAAMmB,IAAIA,CAAC1B,QAAQ,EAAE2B,IAAI,EAAE1B,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAC5B,GAAGC,OAAO;MACVM,MAAM,EAAE,MAAM;MACdqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACH,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA,MAAMI,GAAGA,CAAC/B,QAAQ,EAAE2B,IAAI,EAAE1B,OAAO,GAAG,CAAC,CAAC,EAAE;IACtC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAC5B,GAAGC,OAAO;MACVM,MAAM,EAAE,KAAK;MACbqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACH,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA,MAAMK,MAAMA,CAAChC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAE,GAAGC,OAAO;MAAEM,MAAM,EAAE;IAAS,CAAC,CAAC;EACjE;;EAEA;EACA,MAAM0B,UAAUA,CAACC,IAAI,EAAEjC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAM+B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,MAAM5B,MAAM,GAAG;MACbC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUN,KAAK;MAClC,CAAC;MACD0B,IAAI,EAAEO,QAAQ;MACd,GAAGlC;IACL,CAAC;IAED,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACZ,OAAO,cAAc,EAAEQ,MAAM,CAAC;IAEnE,IAAIG,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACtD,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;IACpC;IAEA,OAAOJ,QAAQ;EACjB;AACF;AAEA,eAAe,IAAIb,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}