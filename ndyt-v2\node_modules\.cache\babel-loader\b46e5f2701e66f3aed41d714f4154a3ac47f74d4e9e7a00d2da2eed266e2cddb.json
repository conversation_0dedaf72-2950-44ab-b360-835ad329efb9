{"ast": null, "code": "export default {\n  name: 'RegisterView',\n  data() {\n    return {\n      username: '',\n      full_name: '',\n      password: '',\n      team_pin: '',\n      governorate: '',\n      busy: false,\n      error: '',\n      governorates: ['بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى', 'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين', 'ميسان', 'دهوك', 'السليمانية', 'حلبجة']\n    };\n  },\n  methods: {\n    async doRegister() {\n      this.error = '';\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\n        this.error = 'يرجى تعبئة جميع الحقول';\n        return;\n      }\n      this.busy = true;\n      try {\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            username: this.username,\n            full_name: this.full_name,\n            password: this.password,\n            team_pin: this.team_pin,\n            governorate: this.governorate\n          })\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\n        // persist auth and team pin\n        localStorage.setItem('ndyt_token', data.token);\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\n        const redirect = this.$route.query.redirect || '/';\n        this.$router.replace(redirect);\n      } catch (e) {\n        this.error = e.message;\n      } finally {\n        this.busy = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "username", "full_name", "password", "team_pin", "governorate", "busy", "error", "governorates", "methods", "doRegister", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "json", "ok", "Error", "localStorage", "setItem", "token", "user", "redirect", "$route", "query", "$router", "replace", "e", "message"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\RegisterView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'RegisterView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      full_name: '',\r\n      password: '',\r\n      team_pin: '',\r\n      governorate: '',\r\n      busy: false,\r\n      error: '',\r\n      governorates: [\r\n        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',\r\n        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',\r\n        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    async doRegister() {\r\n      this.error = '';\r\n      if (!this.username || !this.full_name || !this.password || !this.team_pin || !this.governorate) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/register', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, full_name: this.full_name, password: this.password, team_pin: this.team_pin, governorate: this.governorate })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل إنشاء الحساب');\r\n        // persist auth and team pin\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"register-container\">\r\n    <!-- Animated Background -->\r\n    <div class=\"animated-bg\">\r\n      <div class=\"floating-shapes\">\r\n        <div class=\"shape shape-1\"></div>\r\n        <div class=\"shape shape-2\"></div>\r\n        <div class=\"shape shape-3\"></div>\r\n        <div class=\"shape shape-4\"></div>\r\n        <div class=\"shape shape-5\"></div>\r\n        <div class=\"shape shape-6\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Header Section -->\r\n    <header class=\"header-section\">\r\n      <div class=\"header-content\">\r\n        <div class=\"logo-container\">\r\n          <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n          <div class=\"logo-glow\"></div>\r\n        </div>\r\n        <div class=\"header-text\">\r\n          <h2 class=\"org-name\">المجلس الأعلى للشباب</h2>\r\n          <h3 class=\"team-name\">الفريق الوطني للشباب الرقمي</h3>\r\n        </div>\r\n      </div>\r\n    </header>\r\n\r\n    <!-- Main Content -->\r\n    <main class=\"main-content\">\r\n      <div class=\"register-card\">\r\n        <div class=\"card-header\">\r\n          <div class=\"register-icon\">\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n          </div>\r\n          <h1 class=\"card-title\">إنشاء حساب جديد</h1>\r\n          <p class=\"card-subtitle\">انضم إلى الفريق الوطني للشباب الرقمي</p>\r\n        </div>\r\n\r\n        <form class=\"register-form\" @submit.prevent=\"doRegister\">\r\n          <div class=\"form-row\">\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z\" fill=\"currentColor\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"username\"\r\n                  type=\"text\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\">اسم المستخدم</label>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                    <path d=\"M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"full_name\"\r\n                  type=\"text\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\"> الرباعي واللقب</label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group\">\r\n            <div class=\"select-wrapper\">\r\n              <div class=\"input-icon\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  <path d=\"M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </div>\r\n              <select v-model=\"governorate\" class=\"form-select\" dir=\"rtl\" required>\r\n                <option value=\"\" disabled>اختر المحافظة</option>\r\n                <option v-for=\"gov in governorates\" :key=\"gov\" :value=\"gov\">{{ gov }}</option>\r\n              </select>\r\n              <div class=\"select-arrow\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M6 9L12 15L18 9\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM9 6C9 4.34 10.34 3 12 3S15 4.34 15 6V8H9V6ZM18 20H6V10H18V20ZM12 17C13.1 17 14 16.1 14 15S13.1 13 12 13S10 13.9 10 15S10.9 17 12 17Z\" fill=\"currentColor\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"password\"\r\n                  type=\"password\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\">كلمة المرور</label>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\">\r\n              <div class=\"input-wrapper\">\r\n                <div class=\"input-icon\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  v-model=\"team_pin\"\r\n                  type=\"text\"\r\n                  dir=\"rtl\"\r\n                  placeholder=\"\"\r\n                  class=\"form-input\"\r\n                  required\r\n                />\r\n                <label class=\"floating-label\">رمز الفريق الرقمي</label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <button type=\"submit\" :disabled=\"busy\" class=\"register-btn\">\r\n            <span v-if=\"!busy\" class=\"btn-content\">\r\n              <span class=\"btn-text\">إنشاء الحساب</span>\r\n              <svg class=\"btn-icon\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M22 12H16L14 15H10L8 12H2\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M5.45 5.11L2 12V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H20C20.5304 20 21.0391 19.7893 21.4142 19.4142C21.7893 19.0391 22 18.5304 22 18V12L18.55 5.11C18.3844 4.77679 18.1292 4.49637 17.813 4.30028C17.4967 4.10419 17.1321 4.0002 16.76 4H7.24C6.86792 4.0002 6.50326 4.10419 6.18704 4.30028C5.87083 4.49637 5.61558 4.77679 5.45 5.11Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n            </span>\r\n            <span v-else class=\"btn-loading\">\r\n              <div class=\"loading-spinner\"></div>\r\n              <span>جاري التسجيل...</span>\r\n            </span>\r\n          </button>\r\n\r\n          <div v-if=\"error\" class=\"error-message\">\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z\" fill=\"currentColor\"/>\r\n            </svg>\r\n            <span>{{ error }}</span>\r\n          </div>\r\n        </form>\r\n\r\n        <div class=\"card-footer\">\r\n          <p class=\"login-link\">\r\n            لديك حساب بالفعل؟\r\n            <router-link to=\"/login\" class=\"link\">سجّل الدخول</router-link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n/* Global Styles */\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.register-container {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\r\n  direction: rtl;\r\n}\r\n\r\n/* Animated Background */\r\n.animated-bg {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.floating-shapes {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shape {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  background: linear-gradient(45deg, rgba(79, 70, 229, 0.08), rgba(124, 58, 237, 0.08));\r\n  animation: float 8s ease-in-out infinite;\r\n}\r\n\r\n.shape-1 {\r\n  width: 100px;\r\n  height: 100px;\r\n  top: 15%;\r\n  left: 8%;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.shape-2 {\r\n  width: 140px;\r\n  height: 140px;\r\n  top: 25%;\r\n  right: 12%;\r\n  animation-delay: 2.5s;\r\n}\r\n\r\n.shape-3 {\r\n  width: 80px;\r\n  height: 80px;\r\n  bottom: 35%;\r\n  left: 15%;\r\n  animation-delay: 5s;\r\n}\r\n\r\n.shape-4 {\r\n  width: 120px;\r\n  height: 120px;\r\n  bottom: 15%;\r\n  right: 8%;\r\n  animation-delay: 1.5s;\r\n}\r\n\r\n.shape-5 {\r\n  width: 160px;\r\n  height: 160px;\r\n  top: 45%;\r\n  left: 45%;\r\n  transform: translate(-50%, -50%);\r\n  animation-delay: 3.5s;\r\n}\r\n\r\n.shape-6 {\r\n  width: 90px;\r\n  height: 90px;\r\n  top: 8%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  animation-delay: 4.5s;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px) rotate(0deg);\r\n    opacity: 0.4;\r\n  }\r\n  50% {\r\n    transform: translateY(-25px) rotate(180deg);\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n/* Header Section */\r\n.header-section {\r\n  position: relative;\r\n  z-index: 2;\r\n  padding: 1.5rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  backdrop-filter: blur(10px);\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 2rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.logo-container {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.logo {\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(79, 70, 229, 0.5);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.logo-glow {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 50%;\r\n  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    transform: translate(-50%, -50%) scale(1);\r\n    opacity: 0.5;\r\n  }\r\n  50% {\r\n    transform: translate(-50%, -50%) scale(1.1);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n.header-text {\r\n  text-align: center;\r\n}\r\n\r\n.org-name {\r\n  font-size: clamp(1.2rem, 4vw, 1.8rem);\r\n  font-weight: 800;\r\n  color: #f8fafc;\r\n  margin: 0 0 0.5rem 0;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.team-name {\r\n  font-size: clamp(1rem, 3vw, 1.4rem);\r\n  font-weight: 600;\r\n  color: #cbd5e1;\r\n  margin: 0;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n  position: relative;\r\n  z-index: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: calc(100vh - 140px);\r\n  padding: 2rem 1rem;\r\n}\r\n\r\n.register-card {\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.15);\r\n  border-radius: 24px;\r\n  padding: 2.5rem;\r\n  width: 100%;\r\n  max-width: 600px;\r\n  box-shadow:\r\n    0 20px 40px rgba(0, 0, 0, 0.4),\r\n    0 0 0 1px rgba(255, 255, 255, 0.05);\r\n  transition: all 0.4s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.register-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);\r\n}\r\n\r\n.register-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow:\r\n    0 32px 64px rgba(0, 0, 0, 0.5),\r\n    0 0 0 1px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Card Header */\r\n.card-header {\r\n  text-align: center;\r\n  margin-bottom: 2.5rem;\r\n}\r\n\r\n.register-icon {\r\n  width: 64px;\r\n  height: 64px;\r\n  margin: 0 auto 1.5rem;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);\r\n  animation: iconFloat 3s ease-in-out infinite;\r\n}\r\n\r\n.register-icon svg {\r\n  width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n@keyframes iconFloat {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n.card-title {\r\n  font-size: 2rem;\r\n  font-weight: 800;\r\n  margin: 0 0 0.5rem 0;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.card-subtitle {\r\n  color: #94a3b8;\r\n  font-size: 1rem;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Form Styles */\r\n.register-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.form-row {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 1rem;\r\n}\r\n\r\n.input-group {\r\n  position: relative;\r\n}\r\n\r\n.input-wrapper, .select-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.input-icon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  color: #64748b;\r\n  z-index: 2;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.form-input, .form-select {\r\n  width: 100%;\r\n  padding: 1rem 3rem 1rem 1rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 16px;\r\n  color: #f1f5f9;\r\n  font-size: 1rem;\r\n  transition: all 0.3s ease;\r\n  text-align: right;\r\n  direction: rtl;\r\n}\r\n\r\n.form-select {\r\n  cursor: pointer;\r\n  appearance: none;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n}\r\n\r\n.form-input:focus, .form-select:focus {\r\n  outline: none;\r\n  border-color: #10b981;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);\r\n}\r\n\r\n.form-input:focus + .floating-label,\r\n.form-input:not(:placeholder-shown) + .floating-label,\r\n.form-select:focus + .floating-label,\r\n.form-select:not([value=\"\"]) + .floating-label {\r\n  transform: translateY(-2.5rem) scale(0.85);\r\n  color: #10b981;\r\n  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);\r\n\r\n}\r\n\r\n.form-input:focus ~ .input-icon,\r\n.form-select:focus ~ .input-icon {\r\n  color: #10b981;\r\n}\r\n\r\n.floating-label {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #64748b;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  pointer-events: none;\r\n  padding: 0 0.5rem;\r\n}\r\n\r\n.select-label {\r\n  z-index: 1;\r\n}\r\n\r\n.select-arrow {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  color: #64748b;\r\n  pointer-events: none;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.form-select:focus ~ .select-arrow {\r\n  color: #10b981;\r\n}\r\n\r\n.form-select option {\r\n  background: #1b1f24;\r\n  color: #e5e7eb;\r\n  padding: 0.5rem;\r\n}\r\n\r\n/* Button Styles */\r\n.register-btn {\r\n  width: 100%;\r\n  padding: 1rem 2rem;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  border: none;\r\n  border-radius: 16px;\r\n  color: white;\r\n  font-size: 1.1rem;\r\n  font-weight: 700;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);\r\n  margin-top: 1rem;\r\n}\r\n\r\n.register-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.register-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.register-btn:hover {\r\n  background: linear-gradient(135deg, #34d399, #10b981);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.4);\r\n}\r\n\r\n.register-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.register-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.btn-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.btn-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.register-btn:hover .btn-icon {\r\n  transform: translateX(-4px);\r\n}\r\n\r\n.btn-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-top: 2px solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Error Message */\r\n.error-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem;\r\n  background: rgba(239, 68, 68, 0.1);\r\n  border: 1px solid rgba(239, 68, 68, 0.3);\r\n  border-radius: 12px;\r\n  color: #fca5a5;\r\n  font-size: 0.9rem;\r\n  margin-top: 1rem;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n.error-message svg {\r\n  width: 20px;\r\n  height: 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Card Footer */\r\n.card-footer {\r\n  margin-top: 2rem;\r\n  text-align: center;\r\n}\r\n\r\n.login-link {\r\n  color: #94a3b8;\r\n  font-size: 0.95rem;\r\n  margin: 0;\r\n}\r\n\r\n.link {\r\n  color: #10b981;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.link::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: 0;\r\n  width: 0;\r\n  height: 2px;\r\n  background: linear-gradient(135deg, #10b981, #059669);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.link:hover::after {\r\n  width: 100%;\r\n}\r\n\r\n.link:hover {\r\n  color: #34d399;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .header-section {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1.5rem 1rem;\r\n    min-height: calc(100vh - 120px);\r\n  }\r\n\r\n  .register-card {\r\n    padding: 2rem;\r\n    border-radius: 20px;\r\n    max-width: 500px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .register-icon {\r\n    width: 56px;\r\n    height: 56px;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .register-icon svg {\r\n    width: 28px;\r\n    height: 28px;\r\n  }\r\n\r\n  .form-row {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .form-input, .form-select {\r\n    padding: 0.875rem 2.5rem 0.875rem 0.875rem;\r\n    font-size: 16px; /* Prevents zoom on iOS */\r\n  }\r\n\r\n  .register-btn {\r\n    padding: 0.875rem 1.5rem;\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .header-section {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .logo {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .logo-glow {\r\n    width: 70px;\r\n    height: 70px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 1rem 0.75rem;\r\n  }\r\n\r\n  .register-card {\r\n    padding: 1.5rem;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .card-subtitle {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .register-icon {\r\n    width: 48px;\r\n    height: 48px;\r\n    border-radius: 16px;\r\n  }\r\n\r\n  .register-icon svg {\r\n    width: 24px;\r\n    height: 24px;\r\n  }\r\n\r\n  .register-form {\r\n    gap: 1.25rem;\r\n  }\r\n\r\n  .form-input, .form-select {\r\n    padding: 0.75rem 2.25rem 0.75rem 0.75rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .input-icon {\r\n    left: 0.75rem;\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .floating-label {\r\n    right: 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .select-arrow {\r\n    right: 0.75rem;\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .register-btn {\r\n    padding: 0.75rem 1.25rem;\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .btn-icon {\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n\r\n  .error-message {\r\n    padding: 0.75rem;\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .login-link {\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 360px) {\r\n  .register-card {\r\n    padding: 1.25rem;\r\n  }\r\n\r\n  .card-header {\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.375rem;\r\n  }\r\n\r\n  .form-input, .form-select {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .floating-label {\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n</style>"], "mappings": "AACA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,CACZ,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAC1E,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EACrE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,OAAM;IAEzC,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,UAAUA,CAAA,EAAG;MACjB,IAAI,CAACH,KAAI,GAAI,EAAE;MACf,IAAI,CAAC,IAAI,CAACN,QAAO,IAAK,CAAC,IAAI,CAACC,SAAQ,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,QAAO,IAAK,CAAC,IAAI,CAACC,WAAW,EAAE;QAC9F,IAAI,CAACE,KAAI,GAAI,wBAAwB;QACrC;MACF;MACA,IAAI,CAACD,IAAG,GAAI,IAAI;MAChB,IAAI;QACF,MAAMK,GAAE,GAAI,MAAMC,KAAK,CAAC,uCAAuC,EAAE;UAC/DC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEhB,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,SAAS,EAAE,IAAI,CAACA,SAAS;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAAEC,WAAW,EAAE,IAAI,CAACA;UAAY,CAAC;QAC9J,CAAC,CAAC;QACF,MAAML,IAAG,GAAI,MAAMW,GAAG,CAACO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACP,GAAG,CAACQ,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACpB,IAAI,CAACO,KAAI,IAAK,kBAAkB,CAAC;QAC9D;QACAc,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEtB,IAAI,CAACuB,KAAK,CAAC;QAC9CF,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEN,IAAI,CAACC,SAAS,CAACjB,IAAI,CAACwB,IAAG,IAAK,CAAC,CAAC,CAAC,CAAC;QAClEH,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAClB,QAAQ,CAAC;QACpD,MAAMqB,QAAO,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAO,IAAK,GAAG;QAClD,IAAI,CAACG,OAAO,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,EAAE,OAAOK,CAAC,EAAE;QACV,IAAI,CAACvB,KAAI,GAAIuB,CAAC,CAACC,OAAO;MACxB,UAAU;QACR,IAAI,CAACzB,IAAG,GAAI,KAAK;MACnB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}